<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateExerciseSettingsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'exercise_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'org_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => false,
            ],
            'category' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => false,
                'default'    => 'general',
                'comment'    => 'Setting category: recruitment, interview, ai, scoring, etc.'
            ],
            'setting_key' => [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => false,
                'comment'    => 'Unique setting identifier'
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Setting value (can be JSON for complex data)'
            ],
            'data_type' => [
                'type'       => 'ENUM',
                'constraint' => ['string', 'integer', 'float', 'boolean', 'json', 'date', 'datetime'],
                'default'    => 'string',
                'null'       => false,
                'comment'    => 'Data type for proper casting'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Human-readable description of the setting'
            ],
            'is_active' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
                'null'       => false,
                'comment'    => 'Whether this setting is active'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['exercise_id', 'setting_key'], false, true); // Unique constraint
        $this->forge->addKey('org_id');
        $this->forge->addKey('category');
        $this->forge->addKey('is_active');

        // Foreign key constraints
        $this->forge->addForeignKey('exercise_id', 'exercises', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('org_id', 'dakoii_org', 'id', 'CASCADE', 'CASCADE');

        $this->forge->createTable('exercise_settings');

        // Insert default settings based on current DakoiiOrgModel structure
        $this->insertDefaultSettings();
    }

    public function down()
    {
        $this->forge->dropTable('exercise_settings');
    }

    private function insertDefaultSettings()
    {
        // These are the default setting definitions that can be customized per exercise
        $defaultSettings = [
            // Recruitment Settings
            [
                'category' => 'recruitment',
                'setting_key' => 'advertisement_no',
                'data_type' => 'string',
                'description' => 'Advertisement number for the recruitment exercise'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'advertisement_date',
                'data_type' => 'date',
                'description' => 'Date when the recruitment advertisement was published'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'mode_of_advert',
                'data_type' => 'string',
                'description' => 'Mode of advertisement (newspaper, online, etc.)'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'introduction',
                'data_type' => 'string',
                'description' => 'Introduction text for the recruitment process'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'composition',
                'data_type' => 'string',
                'description' => 'Composition details of the recruitment'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'criteria',
                'data_type' => 'string',
                'description' => 'Selection criteria for the recruitment'
            ],
            [
                'category' => 'recruitment',
                'setting_key' => 'culling',
                'data_type' => 'string',
                'description' => 'Culling process description'
            ],

            // Scoring Settings
            [
                'category' => 'scoring',
                'setting_key' => 'sort_out_of',
                'data_type' => 'integer',
                'description' => 'Maximum score for sorting/ranking applicants'
            ],
            [
                'category' => 'scoring',
                'setting_key' => 'passing_score',
                'data_type' => 'float',
                'description' => 'Minimum passing score for applicants'
            ],
            [
                'category' => 'scoring',
                'setting_key' => 'interview_weight',
                'data_type' => 'float',
                'description' => 'Weight of interview scores in final calculation'
            ],

            // AI Settings
            [
                'category' => 'ai',
                'setting_key' => 'ai_model',
                'data_type' => 'string',
                'description' => 'AI model to use (openai, anthropic, gemini, deepseek)'
            ],
            [
                'category' => 'ai',
                'setting_key' => 'ai_enabled',
                'data_type' => 'boolean',
                'description' => 'Whether AI features are enabled for this exercise'
            ],

            // Interview Settings
            [
                'category' => 'interview',
                'setting_key' => 'interview_duration',
                'data_type' => 'integer',
                'description' => 'Interview duration in minutes per interviewee'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'transition_time',
                'data_type' => 'integer',
                'description' => 'Transition time between interviews in minutes'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'start_time',
                'data_type' => 'string',
                'description' => 'Daily interview start time (HH:MM format)'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'end_time',
                'data_type' => 'string',
                'description' => 'Daily interview end time (HH:MM format)'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'start_date',
                'data_type' => 'date',
                'description' => 'Interview start date'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'break_times',
                'data_type' => 'json',
                'description' => 'Break times during interviews (JSON array)'
            ],
            [
                'category' => 'interview',
                'setting_key' => 'break_days',
                'data_type' => 'json',
                'description' => 'Days when interviews are not conducted (JSON array)'
            ],

            // Notification Settings
            [
                'category' => 'notification',
                'setting_key' => 'email_notifications',
                'data_type' => 'boolean',
                'description' => 'Enable email notifications for this exercise'
            ],
            [
                'category' => 'notification',
                'setting_key' => 'sms_notifications',
                'data_type' => 'boolean',
                'description' => 'Enable SMS notifications for this exercise'
            ],

            // Report Settings
            [
                'category' => 'report',
                'setting_key' => 'include_photos',
                'data_type' => 'boolean',
                'description' => 'Include applicant photos in reports'
            ],
            [
                'category' => 'report',
                'setting_key' => 'report_template',
                'data_type' => 'string',
                'description' => 'Default report template to use'
            ],
        ];

        // Note: These are just the setting definitions
        // Actual values will be set per exercise through the settings interface
        // This migration only creates the structure
    }
}
