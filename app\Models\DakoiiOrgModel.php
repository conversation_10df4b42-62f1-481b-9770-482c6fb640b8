<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiOrgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'orgcode',
        'name',
        'description',
        'org_logo',
        'ai_model', //openai, anthropic, gemini, deepseek,
        'is_active',
        'license_status'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;

    // You can add custom methods here if needed
}
