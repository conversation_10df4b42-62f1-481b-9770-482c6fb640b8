<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseSettingsModel extends Model
{
    protected $table      = 'exercise_settings';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;

    protected $allowedFields = [
        'exercise_id',
        'org_id',
        'advertisement_no',
        'advertisement_date',
        'mode_of_advert',
        'sort_out_of',
        'introduction',
        'composition',
        'criteria',
        'culling',
        'interview_settings'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $validationRules = [
        'exercise_id'   => 'required|integer',
        'org_id'        => 'required|integer',
        'advertisement_date' => 'valid_date[Y-m-d]|permit_empty',
        'sort_out_of'   => 'integer|permit_empty',
        'interview_settings' => 'valid_json|permit_empty'
    ];

    protected $validationMessages = [
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'integer'  => 'Exercise ID must be an integer'
        ],
        'org_id' => [
            'required' => 'Organization ID is required',
            'integer'  => 'Organization ID must be an integer'
        ]
    ];

    /**
     * Get exercise settings for a specific exercise and organization
     */
    public function getExerciseSettings($exerciseId, $orgId = null)
    {
        if ($orgId === null) {
            $orgId = session('org_id');
        }

        return $this->where([
            'exercise_id' => $exerciseId,
            'org_id' => $orgId
        ])->first();
    }

    /**
     * Create or update exercise settings
     */
    public function saveExerciseSettings($exerciseId, $data, $orgId = null)
    {
        if ($orgId === null) {
            $orgId = session('org_id');
        }

        // Add required fields
        $data['exercise_id'] = $exerciseId;
        $data['org_id'] = $orgId;

        // Check if settings already exist
        $existing = $this->where([
            'exercise_id' => $exerciseId,
            'org_id' => $orgId
        ])->first();

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Get a specific setting field value
     */
    public function getSettingField($exerciseId, $fieldName, $default = null)
    {
        $settings = $this->getExerciseSettings($exerciseId);

        if ($settings && isset($settings[$fieldName])) {
            // Handle JSON fields
            if ($fieldName === 'interview_settings' && is_string($settings[$fieldName])) {
                return json_decode($settings[$fieldName], true);
            }
            return $settings[$fieldName];
        }

        return $default;
    }

    /**
     * Set a specific setting field value
     */
    public function setSettingField($exerciseId, $fieldName, $value)
    {
        $orgId = session('org_id');

        // Handle JSON fields
        if ($fieldName === 'interview_settings' && is_array($value)) {
            $value = json_encode($value);
        }

        $data = [$fieldName => $value];
        return $this->saveExerciseSettings($exerciseId, $data, $orgId);
    }

    /**
     * Copy settings from one exercise to another
     */
    public function copySettings($fromExerciseId, $toExerciseId, $orgId = null)
    {
        if ($orgId === null) {
            $orgId = session('org_id');
        }

        $sourceSettings = $this->getExerciseSettings($fromExerciseId, $orgId);

        if (!$sourceSettings) {
            return false;
        }

        // Remove id and update exercise_id
        unset($sourceSettings['id']);
        $sourceSettings['exercise_id'] = $toExerciseId;

        return $this->saveExerciseSettings($toExerciseId, $sourceSettings, $orgId);
    }

    /**
     * Initialize default settings for a new exercise
     */
    public function initializeDefaultSettings($exerciseId, $orgId = null)
    {
        if ($orgId === null) {
            $orgId = session('org_id');
        }

        // Create empty settings record
        $defaultData = [
            'exercise_id' => $exerciseId,
            'org_id' => $orgId,
            'advertisement_no' => null,
            'advertisement_date' => null,
            'mode_of_advert' => null,
            'sort_out_of' => null,
            'introduction' => null,
            'composition' => null,
            'criteria' => null,
            'culling' => null,
            'interview_settings' => null
        ];

        return $this->insert($defaultData);
    }

    /**
     * Migrate settings from DakoiiOrgModel to exercise settings
     */
    public function migrateFromOrgSettings($exerciseId, $orgData, $orgId = null)
    {
        if ($orgId === null) {
            $orgId = session('org_id');
        }

        // Map the core fields from DakoiiOrgModel
        $migrationData = [
            'exercise_id' => $exerciseId,
            'org_id' => $orgId,
            'advertisement_no' => $orgData['advertisement_no'] ?? null,
            'advertisement_date' => $orgData['advertisement_date'] ?? null,
            'mode_of_advert' => $orgData['mode_of_advert'] ?? null,
            'sort_out_of' => $orgData['sort_out_of'] ?? null,
            'introduction' => $orgData['introduction'] ?? null,
            'composition' => $orgData['composition'] ?? null,
            'criteria' => $orgData['criteria'] ?? null,
            'culling' => $orgData['culling'] ?? null,
            'interview_settings' => $orgData['interview_settings'] ?? null
        ];

        return $this->saveExerciseSettings($exerciseId, $migrationData, $orgId);
    }
}
