<?php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;
use App\Models\ExerciseModel;

/**
 * Exercise Filter
 * 
 * This filter ensures that exercise context is properly set and validated
 * for routes that require exercise context
 */
class ExerciseFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Load exercise helper
        helper('exercise');
        
        // Get current URI path
        $uri = $request->getUri()->getPath();
        
        // Skip filter for exercise management routes
        if (strpos($uri, '/exercise') === 0) {
            return;
        }
        
        // Skip filter for dashboard, login, logout, and other system routes
        $skipRoutes = [
            '/dashboard',
            '/login',
            '/logout',
            '/home',
            '/',
            '/dakoii'
        ];
        
        foreach ($skipRoutes as $skipRoute) {
            if (strpos($uri, $skipRoute) === 0) {
                return;
            }
        }
        
        // Check if exercise context is set
        if (!has_exercise_context()) {
            return redirect()->to('/exercise')
                ->with('error', 'Please select an exercise to continue');
        }
        
        // Validate exercise belongs to current organization
        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');
        
        if (!$orgId) {
            return redirect()->to('/')
                ->with('error', 'Please login first');
        }
        
        if (!validate_exercise_access($exerciseId, $orgId)) {
            // Clear invalid exercise context
            clear_exercise_context();
            
            return redirect()->to('/exercise')
                ->with('error', 'Invalid exercise context. Please select a valid exercise.');
        }
        
        // Refresh exercise data in session if needed
        $this->refreshExerciseContext($exerciseId);
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here
    }
    
    /**
     * Refresh exercise context data in session
     *
     * @param int $exerciseId
     * @return void
     */
    private function refreshExerciseContext($exerciseId)
    {
        $currentExercise = get_current_exercise();
        
        // If exercise data is not in session or is outdated, refresh it
        if (!$currentExercise || 
            !isset($currentExercise['updated_at']) || 
            strtotime($currentExercise['updated_at']) < (time() - 3600)) { // Refresh if older than 1 hour
            
            $exerciseModel = new ExerciseModel();
            $exercise = $exerciseModel->find($exerciseId);
            
            if ($exercise) {
                set_exercise_context($exerciseId, $exercise);
            }
        }
    }
}
