<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cumulative Interview Report - <?= esc($position['designation']) ?></title>
    <style>
        @page {
            size: A4 landscape;
            margin: 1cm 1cm 2.5cm 1cm; /* Extra bottom margin for footer */
            counter-increment: page;

            @bottom-left {
                content: "Page " counter(page) " of " counter(pages) "\A File Code: <?= esc($uniqueCode) ?>";
                font-size: 10px;
                color: #666;
                white-space: pre;
            }

            @bottom-center {
                content: "SelMasta - HR Selection Management System | <?= base_url() ?>";
                font-size: 10px;
                color: #666;
                font-weight: bold;
            }

            @bottom-right {
                content: "Generated: <?= date('M d, Y H:i') ?>";
                font-size: 10px;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            counter-reset: page;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .header h3 {
            margin: 5px 0;
            font-size: 12px;
            color: #888;
        }
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 6px 4px;
            text-align: center;
            vertical-align: middle;
            page-break-inside: avoid; /* Prevent row breaking across pages */
        }

        tr {
            page-break-inside: avoid; /* Keep table rows together */
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 9px;
        }
        
        .rank-col {
            width: 8%;
        }
        
        .name-col {
            width: 25%;
            text-align: left;
        }
        
        .age-col {
            width: 8%;
        }
        
        .rating-col {
            width: 12%;
        }
        
        .interview-col {
            width: 12%;
        }
        
        .cumulative-col {
            width: 12%;
        }
        
        .percentage-col {
            width: 10%;
        }
        
        .status-col {
            width: 13%;
        }
        
        .footer {
            position: relative;
            width: 100%;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding: 10px;
            background-color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            margin-top: 20px;
            page-break-inside: avoid;
        }

        .footer-left {
            text-align: left;
            flex: 1;
        }

        .footer-center {
            text-align: center;
            flex: 2;
        }

        .footer-right {
            text-align: right;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .file-code {
            font-weight: bold;
            font-size: 9px;
            color: #333;
        }
        
        .rank-number {
            font-weight: bold;
            font-size: 11px;
        }
        
        .applicant-name {
            font-weight: bold;
        }
        
        .cumulative-score {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        
        .print-date {
            font-size: 9px;
            color: #999;
            margin-top: 10px;
        }

        .content-area {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><?= esc($organization['name']) ?></h1>
        <h2><?= esc($positionGroup['group_name']) ?></h2>
        <h3><?= esc($position['designation']) ?> - Cumulative Interview Results Report</h3>
        <div class="print-date">Generated on: <?= date('F d, Y \a\t g:i A') ?></div>
    </div>

    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th class="rank-col">Rank</th>
                    <th class="name-col">Applicant Name</th>
                    <th class="age-col">Age</th>
                    <th class="rating-col">Rating Score</th>
                    <th class="interview-col">Interview Score</th>
                    <th class="cumulative-col">Cumulative Score</th>
                    <th class="percentage-col">Percentage</th>
                    <th class="status-col">Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($applicants)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 20px;">
                            No applicants found for this position.
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($applicants as $applicant): ?>
                        <?php
                            $maxPossibleRating = 100; // Assuming max rating is 100
                            $maxPossibleTotal = $maxPossibleRating + $totalPossibleInterviewScore;
                            $percentage = $maxPossibleTotal > 0 ? ($applicant['cumulative_total'] / $maxPossibleTotal) * 100 : 0;
                        ?>
                        <tr>
                            <td class="rank-number"><?= $applicant['rank'] ?></td>
                            <td class="applicant-name" style="text-align: left;">
                                <?= esc($applicant['name']) ?><br>
                                <small><?= esc($applicant['sex']) ?></small>
                            </td>
                            <td><?= esc($applicant['age']) ?></td>
                            <td>
                                <?= number_format($applicant['rating_total'], 1) ?><br>
                                <small>out of <?= $applicant['max_rating_total'] ?></small>
                            </td>
                            <td>
                                <?= number_format($applicant['total_interview_score'], 1) ?><br>
                                <small>out of <?= $totalPossibleInterviewScore ?></small>
                            </td>
                            <td class="cumulative-score">
                                <?= number_format($applicant['cumulative_total'], 1) ?>
                            </td>
                            <td><?= number_format($percentage, 1) ?>%</td>
                            <td>
                                <!-- Status removed as requested -->
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>



    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully rendered
            setTimeout(function() {
                window.print();
            }, 1000);

            // Close window after printing (optional)
            window.onafterprint = function() {
                window.close();
            };
        };
    </script>

    <style>
        /* Print-specific styles */
        @media print {
            body {
                counter-reset: page 1;
                margin: 0;
                padding: 0;
            }

            /* Prevent table rows from breaking */
            tr {
                page-break-inside: avoid !important;
            }

            /* Hide any remaining footer elements since we use @page */
            .footer {
                display: none !important;
            }
        }

        /* Screen display - show JavaScript-calculated page numbers */
        @media screen {
            .page-info {
                display: inline;
            }
        }
    </style>
</body>
</html>
