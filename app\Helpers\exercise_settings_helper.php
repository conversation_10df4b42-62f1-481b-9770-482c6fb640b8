<?php

if (!function_exists('get_exercise_setting')) {
    /**
     * Get a setting field value for the current exercise
     *
     * @param string $fieldName The field name to retrieve
     * @param mixed $default Default value if setting not found
     * @return mixed The setting value
     */
    function get_exercise_setting($fieldName, $default = null)
    {
        $exerciseId = get_current_exercise_id();
        if (!$exerciseId) {
            return $default;
        }

        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->getSettingField($exerciseId, $fieldName, $default);
    }
}

if (!function_exists('set_exercise_setting')) {
    /**
     * Set a setting field value for the current exercise
     *
     * @param string $fieldName The field name
     * @param mixed $value The setting value
     * @return bool Success status
     */
    function set_exercise_setting($fieldName, $value)
    {
        $exerciseId = get_current_exercise_id();
        if (!$exerciseId) {
            return false;
        }

        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->setSettingField($exerciseId, $fieldName, $value);
    }
}

if (!function_exists('get_all_exercise_settings')) {
    /**
     * Get all settings for current exercise
     *
     * @return array All settings as associative array
     */
    function get_all_exercise_settings()
    {
        $exerciseId = get_current_exercise_id();
        if (!$exerciseId) {
            return [];
        }

        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->getExerciseSettings($exerciseId);
    }
}

if (!function_exists('initialize_exercise_settings')) {
    /**
     * Initialize default settings for an exercise
     * 
     * @param int $exerciseId Exercise ID
     * @return int Number of settings initialized
     */
    function initialize_exercise_settings($exerciseId)
    {
        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->initializeDefaultSettings($exerciseId);
    }
}

if (!function_exists('get_interview_settings')) {
    /**
     * Get interview settings JSON for current exercise (exact match with DakoiiOrgModel)
     *
     * @return array|null Interview settings JSON or null if not set
     */
    function get_interview_settings()
    {
        return get_exercise_setting('interview_settings');
    }
}

if (!function_exists('set_interview_settings')) {
    /**
     * Set interview settings as JSON structure (exact match with DakoiiOrgModel)
     *
     * @param array $settings Interview settings array
     * @return bool Success status
     */
    function set_interview_settings($settings)
    {
        return set_exercise_setting('interview_settings', $settings);
    }
}

if (!function_exists('get_sort_out_of')) {
    /**
     * Get maximum score for sorting/ranking (core DakoiiOrgModel field)
     *
     * @return int|null Maximum score or null if not set
     */
    function get_sort_out_of()
    {
        $value = get_exercise_setting('sort_out_of');
        return $value !== null ? (int) $value : null;
    }
}

if (!function_exists('copy_exercise_settings')) {
    /**
     * Copy settings from one exercise to another
     * 
     * @param int $fromExerciseId Source exercise ID
     * @param int $toExerciseId Target exercise ID
     * @return int Number of settings copied
     */
    function copy_exercise_settings($fromExerciseId, $toExerciseId)
    {
        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->copySettings($fromExerciseId, $toExerciseId);
    }
}

if (!function_exists('get_exercise_setting_categories')) {
    /**
     * Get available setting categories for current exercise
     * 
     * @return array Available categories
     */
    function get_exercise_setting_categories()
    {
        $exerciseId = get_current_exercise_id();
        if (!$exerciseId) {
            return [];
        }

        $settingsModel = new \App\Models\ExerciseSettingsModel();
        return $settingsModel->getCategories($exerciseId);
    }
}

if (!function_exists('get_advertisement_no')) {
    /**
     * Get advertisement number for current exercise
     *
     * @return string Advertisement number
     */
    function get_advertisement_no()
    {
        return get_exercise_setting('advertisement_no');
    }
}

if (!function_exists('get_advertisement_date')) {
    /**
     * Get advertisement date for current exercise (no fallback)
     *
     * @return string|null Advertisement date or null if not set
     */
    function get_advertisement_date()
    {
        return get_exercise_setting('advertisement_date');
    }
}

if (!function_exists('get_mode_of_advert')) {
    /**
     * Get mode of advertisement for current exercise (no fallback)
     *
     * @return string|null Mode of advertisement or null if not set
     */
    function get_mode_of_advert()
    {
        return get_exercise_setting('mode_of_advert');
    }
}

if (!function_exists('get_introduction')) {
    /**
     * Get introduction text for current exercise (no fallback)
     *
     * @return string|null Introduction text or null if not set
     */
    function get_introduction()
    {
        return get_exercise_setting('introduction');
    }
}

if (!function_exists('get_composition')) {
    /**
     * Get composition details for current exercise (no fallback)
     *
     * @return string|null Composition details or null if not set
     */
    function get_composition()
    {
        return get_exercise_setting('composition');
    }
}

if (!function_exists('get_criteria')) {
    /**
     * Get selection criteria for current exercise (no fallback)
     *
     * @return string|null Selection criteria or null if not set
     */
    function get_criteria()
    {
        return get_exercise_setting('criteria');
    }
}

if (!function_exists('get_culling')) {
    /**
     * Get culling process description for current exercise (no fallback)
     *
     * @return string|null Culling process or null if not set
     */
    function get_culling()
    {
        return get_exercise_setting('culling');
    }
}

if (!function_exists('migrate_org_settings_to_exercise')) {
    /**
     * Migrate organization settings to current exercise
     *
     * @return int Number of settings migrated
     */
    function migrate_org_settings_to_exercise()
    {
        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        if (!$exerciseId || !$orgId) {
            return 0;
        }

        $orgModel = new \App\Models\DakoiiOrgModel();
        $settingsModel = new \App\Models\ExerciseSettingsModel();

        $orgData = $orgModel->find($orgId);
        if (!$orgData) {
            return 0;
        }

        return $settingsModel->migrateFromOrgSettings($exerciseId, $orgData);
    }
}

if (!function_exists('format_setting_value')) {
    /**
     * Format a setting value for display
     *
     * @param mixed $value The setting value
     * @param string $dataType The data type
     * @return string Formatted value
     */
    function format_setting_value($value, $dataType)
    {
        if ($value === null) {
            return 'Not set';
        }

        switch ($dataType) {
            case 'boolean':
                return $value ? 'Yes' : 'No';
            case 'json':
                return is_array($value) ? json_encode($value, JSON_PRETTY_PRINT) : $value;
            case 'date':
                return date('Y-m-d', strtotime($value));
            case 'datetime':
                return date('Y-m-d H:i:s', strtotime($value));
            default:
                return (string) $value;
        }
    }
}
