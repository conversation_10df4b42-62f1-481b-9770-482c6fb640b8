# Exercise Integration System Design & Implementation Guide

## Overview

The Exercise Integration feature transforms the SelMasta system from a single-round recruitment system to a multi-round exercise-based system. This allows organizations to run multiple recruitment cycles without data conflicts, with each exercise containing its own set of applicants, positions, interviewers, and questions.

## Problem Statement

**Current Issue**: The existing system only supports one recruitment cycle per organization. When an organization wants to run a new recruitment round, they cannot do so because:
- All data (applicants, positions, interviewers, questions) is tied directly to the organization
- No way to separate data between different recruitment cycles
- Historical data gets mixed with new recruitment data

**Solution**: Introduce Exercise-based data grouping where each recruitment cycle is an "Exercise" with its own isolated data set.

## System Architecture Changes

### 1. Database Structure Enhancement

The exercise system introduces a hierarchical data structure:

```
Organization
├── Exercise 1 (e.g., "2024 Q1 Recruitment")
│   ├── Positions
│   ├── Position Groups
│   ├── Applicants
│   ├── Interviewers
│   ├── Interview Questions
│   └── Selection Data
├── Exercise 2 (e.g., "2024 Q3 Recruitment")
│   ├── Positions
│   ├── Position Groups
│   ├── Applicants
│   └── ...
└── Exercise N
```

### 2. New Navigation Flow

**Old Navigation**: Dashboard → Feature (e.g., Applicants)
**New Navigation**: Dashboard → Exercise Selection → Feature (e.g., Applicants)

**User Story**:
1. User logs into Dashboard
2. User selects "Exercises" from main menu
3. User either:
   - Creates a new exercise, OR
   - Selects an existing exercise
4. User navigates to specific features (Applicants, Positions, etc.)
5. All data displayed/managed is filtered by the selected exercise

## Implementation Strategy

### Phase 1: Core Exercise Management

#### 1.1 Exercise Controller (`app/Controllers/Exercise.php`)

```php
<?php
namespace App\Controllers;

use App\Models\ExerciseModel;

class Exercise extends BaseController
{
    // Core CRUD operations
    public function index()           // List all exercises for organization
    public function create()          // Show create exercise form
    public function store()           // Save new exercise
    public function show($id)         // Display exercise details
    public function edit($id)         // Show edit exercise form
    public function update($id)       // Update exercise
    public function delete($id)       // Delete exercise
    
    // Status management
    public function activate($id)     // Set exercise status to 'active'
    public function complete($id)     // Set exercise status to 'completed'
    public function archive($id)      // Set exercise status to 'archived'
    
    // Exercise selection for session
    public function select($id)       // Set selected exercise in session
}
```

#### 1.2 Exercise Views Structure

```
app/Views/exercise/
├── index.php              # List all exercises
├── create.php             # Create new exercise form
├── edit.php               # Edit exercise form
├── show.php               # Exercise details view
└── partials/
    ├── exercise_card.php  # Exercise card component
    └── status_badge.php   # Status display component
```

### Phase 2: Session-Based Exercise Context

#### 2.1 Exercise Session Management

Implement session-based exercise context to maintain selected exercise across the application:

```php
// In BaseController or helper
class ExerciseContext
{
    public static function setCurrentExercise($exerciseId)
    {
        session()->set('current_exercise_id', $exerciseId);
    }
    
    public static function getCurrentExercise()
    {
        return session()->get('current_exercise_id');
    }
    
    public static function clearCurrentExercise()
    {
        session()->remove('current_exercise_id');
    }
}
```

#### 2.2 Exercise Middleware/Filter

Create a filter to ensure exercise context is maintained:

```php
// app/Filters/ExerciseFilter.php
class ExerciseFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if exercise is selected for protected routes
        // Redirect to exercise selection if not set
    }
}
```

### Phase 3: Update Existing Controllers

#### 3.1 Controller Update Pattern

All existing controllers need to be updated to include exercise_id in CRUD operations:

**Example: ApplicantController Updates**

```php
// OLD: public function index()
public function index()
{
    $exerciseId = ExerciseContext::getCurrentExercise();
    if (!$exerciseId) {
        return redirect()->to('/exercise')->with('error', 'Please select an exercise first');
    }
    
    $applicants = $this->applicantModel
        ->where('exercise_id', $exerciseId)
        ->where('org_id', session('org_id'))
        ->findAll();
    
    return view('applicant/index', ['applicants' => $applicants]);
}

// OLD: public function store()
public function store()
{
    $data = $this->request->getPost();
    $data['exercise_id'] = ExerciseContext::getCurrentExercise();
    $data['org_id'] = session('org_id');
    
    if ($this->applicantModel->save($data)) {
        return redirect()->to('/applicant')->with('success', 'Applicant created successfully');
    }
    // ... error handling
}
```

#### 3.2 Controllers to Update

1. **ApplicantController** - Filter applicants by exercise_id
2. **PositionController** - Filter positions by exercise_id  
3. **InterviewerController** - Filter interviewers by exercise_id
4. **InterviewQuestionController** - Filter questions by exercise_id
5. **PositionsGroupController** - Filter position groups by exercise_id
6. **InterviewController** - Filter interview data by exercise_id
7. **ReportController** - Generate reports per exercise

### Phase 4: View Updates

#### 4.1 Exercise Context Display

Add exercise context indicator to all relevant views:

```php
<!-- In view headers -->
<div class="exercise-context-bar">
    <span class="badge badge-primary">
        Current Exercise: <?= esc($currentExercise['exercise_title']) ?>
        <span class="badge badge-<?= $statusClass ?>"><?= esc($currentExercise['status']) ?></span>
    </span>
    <a href="/exercise" class="btn btn-sm btn-outline-secondary">Change Exercise</a>
</div>
```

#### 4.2 Navigation Updates

Update main navigation to include exercise selection:

```php
<!-- Main Navigation -->
<li class="nav-item">
    <a href="/exercise" class="nav-link">
        <i class="fas fa-tasks"></i>
        <p>Exercises</p>
    </a>
</li>

<!-- Sub-navigation (only show when exercise is selected) -->
<?php if (ExerciseContext::getCurrentExercise()): ?>
<li class="nav-item">
    <a href="/applicant" class="nav-link">
        <i class="fas fa-users"></i>
        <p>Applicants</p>
    </a>
</li>
<!-- ... other menu items -->
<?php endif; ?>
```

## Implementation Checklist

### ✅ Database & Models (Completed)
- [x] Create exercises table
- [x] Add exercise_id to related tables
- [x] Create ExerciseModel
- [x] Update existing models with exercise_id

### 🔄 Phase 1: Core Exercise Management
- [ ] Create ExerciseController with full CRUD
- [ ] Create exercise views (index, create, edit, show)
- [ ] Implement exercise status management
- [ ] Add exercise validation rules

### 🔄 Phase 2: Session Management
- [ ] Create ExerciseContext helper class
- [ ] Implement exercise session management
- [ ] Create ExerciseFilter for route protection
- [ ] Update routes with exercise filter

### 🔄 Phase 3: Controller Updates
- [ ] Update ApplicantController
- [ ] Update PositionController
- [ ] Update InterviewerController
- [ ] Update InterviewQuestionController
- [ ] Update PositionsGroupController
- [ ] Update InterviewController
- [ ] Update ReportController

### 🔄 Phase 4: View Updates
- [ ] Add exercise context display to all views
- [ ] Update navigation menu
- [ ] Update form submissions to include exercise_id
- [ ] Update data tables to show exercise context

### 🔄 Phase 5: Testing & Validation
- [ ] Test exercise creation and management
- [ ] Test data isolation between exercises
- [ ] Test navigation flow
- [ ] Test CRUD operations with exercise context
- [ ] Test reporting per exercise

## Data Migration Strategy

### For Existing Data

```sql
-- Step 1: Create a default exercise for existing data
INSERT INTO exercises (org_id, exercise_title, description, status, created_by, created_at, updated_at)
SELECT DISTINCT org_id, 'Legacy Data - Default Exercise', 'Migrated from pre-exercise system', 'completed', 1, NOW(), NOW()
FROM applicants;

-- Step 2: Update existing records with the default exercise_id
UPDATE applicants SET exercise_id = (
    SELECT id FROM exercises WHERE org_id = applicants.org_id AND exercise_title = 'Legacy Data - Default Exercise'
);

-- Repeat for other tables...
```

## Security Considerations

1. **Exercise Access Control**: Users should only access exercises within their organization
2. **Data Isolation**: Ensure exercise_id filtering is applied consistently
3. **Session Security**: Validate exercise ownership before setting in session
4. **Permission Checks**: Verify user permissions for exercise operations

## Performance Considerations

1. **Database Indexing**: Add indexes on exercise_id columns
2. **Query Optimization**: Use exercise_id in WHERE clauses consistently  
3. **Caching**: Cache current exercise data in session
4. **Pagination**: Implement pagination for large exercise lists

## Future Enhancements

1. **Exercise Templates**: Create templates for common exercise types
2. **Exercise Cloning**: Allow copying exercises with data
3. **Exercise Analytics**: Dashboard showing exercise statistics
4. **Exercise Archiving**: Automated archiving of old exercises
5. **Multi-Organization Exercises**: Support for shared exercises

This design provides a robust foundation for multi-round recruitment management while maintaining backward compatibility and ensuring data integrity.

## Detailed Implementation Examples

### Exercise Controller Implementation

```php
<?php
namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\ApplicantModel;
use App\Models\PositionModel;

class Exercise extends BaseController
{
    protected $exerciseModel;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        helper(['form', 'url']);
    }

    public function index()
    {
        $orgId = session('org_id');
        $exercises = $this->exerciseModel->getExercisesByOrg($orgId);

        // Add statistics to each exercise
        foreach ($exercises as &$exercise) {
            $exercise['stats'] = $this->getExerciseStats($exercise['id']);
        }

        $data = [
            'exercises' => $exercises,
            'title' => 'Exercise Management'
        ];

        return view('exercise/index', $data);
    }

    public function create()
    {
        $data = [
            'title' => 'Create New Exercise',
            'validation' => \Config\Services::validation()
        ];

        return view('exercise/create', $data);
    }

    public function store()
    {
        $rules = [
            'exercise_title' => 'required|max_length[255]',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return view('exercise/create', [
                'validation' => $this->validator,
                'title' => 'Create New Exercise'
            ]);
        }

        $data = [
            'org_id' => session('org_id'),
            'exercise_title' => $this->request->getPost('exercise_title'),
            'description' => $this->request->getPost('description'),
            'status' => 'draft',
            'created_by' => session('user_id')
        ];

        if ($this->exerciseModel->save($data)) {
            return redirect()->to('/exercise')->with('success', 'Exercise created successfully');
        }

        return redirect()->back()->with('error', 'Failed to create exercise');
    }

    public function select($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != session('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        session()->set('current_exercise_id', $id);
        session()->set('current_exercise', $exercise);

        return redirect()->to('/dashboard')->with('success', 'Exercise selected: ' . $exercise['exercise_title']);
    }

    private function getExerciseStats($exerciseId)
    {
        $applicantModel = new ApplicantModel();
        $positionModel = new PositionModel();

        return [
            'applicants_count' => $applicantModel->where('exercise_id', $exerciseId)->countAllResults(),
            'positions_count' => $positionModel->where('exercise_id', $exerciseId)->countAllResults(),
            'status' => 'active' // This would be calculated based on actual data
        ];
    }
}
```

### Exercise Context Helper

```php
<?php
// app/Helpers/exercise_helper.php

if (!function_exists('get_current_exercise')) {
    function get_current_exercise()
    {
        return session('current_exercise');
    }
}

if (!function_exists('get_current_exercise_id')) {
    function get_current_exercise_id()
    {
        return session('current_exercise_id');
    }
}

if (!function_exists('has_exercise_context')) {
    function has_exercise_context()
    {
        return !empty(session('current_exercise_id'));
    }
}

if (!function_exists('require_exercise_context')) {
    function require_exercise_context()
    {
        if (!has_exercise_context()) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Exercise context required');
        }
    }
}
```

### Updated Applicant Controller Example

```php
<?php
namespace App\Controllers;

use App\Models\ApplicantModel;
use App\Models\PositionModel;
use App\Models\PositionsGroupModel;

class Applicant extends BaseController
{
    protected $applicantModel;

    public function __construct()
    {
        $this->applicantModel = new ApplicantModel();
        helper(['form', 'url', 'exercise']);
    }

    public function index()
    {
        // Ensure exercise context exists
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        $applicants = $this->applicantModel
            ->select('applicants.*, positions.designation, positions_groups.group_name')
            ->join('positions', 'positions.id = applicants.position_id', 'left')
            ->join('positions_groups', 'positions_groups.id = applicants.position_group_id', 'left')
            ->where('applicants.exercise_id', $exerciseId)
            ->where('applicants.org_id', $orgId)
            ->orderBy('applicants.created_at', 'DESC')
            ->findAll();

        $data = [
            'applicants' => $applicants,
            'current_exercise' => get_current_exercise(),
            'title' => 'Applicants Management'
        ];

        return view('applicant/index', $data);
    }

    public function create()
    {
        require_exercise_context();

        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        // Get positions and position groups for this exercise
        $positionModel = new PositionModel();
        $positionsGroupModel = new PositionsGroupModel();

        $positions = $positionModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->where('is_active', 1)
            ->findAll();

        $positionGroups = $positionsGroupModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->findAll();

        $data = [
            'positions' => $positions,
            'position_groups' => $positionGroups,
            'current_exercise' => get_current_exercise(),
            'title' => 'Add New Applicant',
            'validation' => \Config\Services::validation()
        ];

        return view('applicant/create', $data);
    }

    public function store()
    {
        require_exercise_context();

        $rules = [
            'name' => 'required|max_length[255]',
            'position_id' => 'required|integer',
            'position_group_id' => 'required|integer',
            // ... other validation rules
        ];

        if (!$this->validate($rules)) {
            return $this->create(); // Return to form with validation errors
        }

        $data = $this->request->getPost();
        $data['exercise_id'] = get_current_exercise_id();
        $data['org_id'] = session('org_id');

        if ($this->applicantModel->save($data)) {
            return redirect()->to('/applicant')->with('success', 'Applicant added successfully');
        }

        return redirect()->back()->with('error', 'Failed to add applicant');
    }

    public function edit($id)
    {
        require_exercise_context();

        $applicant = $this->applicantModel
            ->where('id', $id)
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', session('org_id'))
            ->first();

        if (!$applicant) {
            return redirect()->to('/applicant')->with('error', 'Applicant not found');
        }

        // Get positions and position groups for this exercise
        $positionModel = new PositionModel();
        $positionsGroupModel = new PositionsGroupModel();

        $positions = $positionModel
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', session('org_id'))
            ->where('is_active', 1)
            ->findAll();

        $positionGroups = $positionsGroupModel
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', session('org_id'))
            ->findAll();

        $data = [
            'applicant' => $applicant,
            'positions' => $positions,
            'position_groups' => $positionGroups,
            'current_exercise' => get_current_exercise(),
            'title' => 'Edit Applicant',
            'validation' => \Config\Services::validation()
        ];

        return view('applicant/edit', $data);
    }

    public function update($id)
    {
        require_exercise_context();

        $applicant = $this->applicantModel
            ->where('id', $id)
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', session('org_id'))
            ->first();

        if (!$applicant) {
            return redirect()->to('/applicant')->with('error', 'Applicant not found');
        }

        $rules = [
            'name' => 'required|max_length[255]',
            'position_id' => 'required|integer',
            'position_group_id' => 'required|integer',
            // ... other validation rules
        ];

        if (!$this->validate($rules)) {
            return $this->edit($id); // Return to form with validation errors
        }

        $data = $this->request->getPost();
        // Ensure exercise_id and org_id cannot be changed
        unset($data['exercise_id'], $data['org_id']);

        if ($this->applicantModel->update($id, $data)) {
            return redirect()->to('/applicant')->with('success', 'Applicant updated successfully');
        }

        return redirect()->back()->with('error', 'Failed to update applicant');
    }

    public function delete($id)
    {
        require_exercise_context();

        $applicant = $this->applicantModel
            ->where('id', $id)
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', session('org_id'))
            ->first();

        if (!$applicant) {
            return redirect()->to('/applicant')->with('error', 'Applicant not found');
        }

        if ($this->applicantModel->delete($id)) {
            return redirect()->to('/applicant')->with('success', 'Applicant deleted successfully');
        }

        return redirect()->to('/applicant')->with('error', 'Failed to delete applicant');
    }
}
```

### Route Configuration Updates

```php
// app/Config/Routes.php

// Exercise management routes
$routes->group('exercise', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Exercise::index');
    $routes->get('create', 'Exercise::create');
    $routes->post('store', 'Exercise::store');
    $routes->get('show/(:num)', 'Exercise::show/$1');
    $routes->get('edit/(:num)', 'Exercise::edit/$1');
    $routes->post('update/(:num)', 'Exercise::update/$1');
    $routes->post('delete/(:num)', 'Exercise::delete/$1');
    $routes->get('select/(:num)', 'Exercise::select/$1');
    $routes->post('activate/(:num)', 'Exercise::activate/$1');
    $routes->post('complete/(:num)', 'Exercise::complete/$1');
    $routes->post('archive/(:num)', 'Exercise::archive/$1');
});

// Exercise-context dependent routes
$routes->group('', ['filter' => ['auth', 'exercise']], function($routes) {
    $routes->resource('applicant', ['controller' => 'Applicant']);
    $routes->resource('position', ['controller' => 'Position']);
    $routes->resource('interviewer', ['controller' => 'Interviewer']);
    $routes->resource('interview-question', ['controller' => 'InterviewQuestion']);
    $routes->resource('position-group', ['controller' => 'PositionsGroup']);
    $routes->resource('interview', ['controller' => 'Interview']);
});
```

### Exercise Filter Implementation

```php
<?php
// app/Filters/ExerciseFilter.php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class ExerciseFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Skip filter for exercise management routes
        $uri = $request->getUri()->getPath();
        if (strpos($uri, '/exercise') === 0) {
            return;
        }

        // Check if exercise context is set
        if (!session('current_exercise_id')) {
            return redirect()->to('/exercise')
                ->with('error', 'Please select an exercise to continue');
        }

        // Validate exercise belongs to current organization
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercise = $exerciseModel->find(session('current_exercise_id'));

        if (!$exercise || $exercise['org_id'] != session('org_id')) {
            session()->remove('current_exercise_id');
            session()->remove('current_exercise');

            return redirect()->to('/exercise')
                ->with('error', 'Invalid exercise context');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do here
    }
}
```

This comprehensive guide provides the complete framework for implementing the exercise-based system, ensuring data isolation, proper navigation flow, and maintaining system integrity.
