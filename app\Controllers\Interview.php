<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\ApplicantModel;
use App\Models\InterviewQuestionModel;
use App\Models\InterviewerModel;
use App\Models\InterviewDataModel;

class Interview extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $applicantModel;
    protected $interviewQuestionModel;
    protected $interviewerModel;
    protected $interviewDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'exercise']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->applicantModel = new ApplicantModel();
        $this->interviewQuestionModel = new InterviewQuestionModel();
        $this->interviewerModel = new InterviewerModel();
        $this->interviewDataModel = new InterviewDataModel();
    }

    /**
     * Display list of positions due for interview
     * GET /interviews
     */
    public function index()
    {
        // Ensure exercise context exists
        require_exercise_context();

        $data['title'] = 'Interviews';
        $data['menu'] = 'interviews';
        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        // Get position groups ordered by priority
        $positionGroups = $this->positionsGroupModel
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->orderBy('priority', 'ASC')
            ->orderBy('group_name', 'ASC')
            ->findAll();

        $interviewData = [];

        foreach ($positionGroups as $group) {
            // Get positions marked for interview in this group
            $positions = $this->positionModel->where('position_group_id', $group['id'])
                ->where('exercise_id', $exerciseId)
                ->where('org_id', $orgId)
                ->where('for_interview', 1)
                ->where('is_active', 1)
                ->orderBy('position_no', 'ASC')
                ->findAll();

            if (!empty($positions)) {
                $groupData = [
                    'id' => $group['id'],
                    'group_name' => $group['group_name'],
                    'priority' => $group['priority'],
                    'description' => $group['description'],
                    'positions' => []
                ];

                foreach ($positions as $position) {
                    // Count shortlisted applicants for this position
                    $shortlistedCount = $this->applicantModel->where('position_id', $position['id'])
                        ->where('org_id', $orgId)
                        ->where('application_status', 'Shortlisted')
                        ->countAllResults();

                    if ($shortlistedCount > 0) {
                        // Get questions count for this position
                        $questions = $this->interviewQuestionModel->getQuestionsByPosition($position['id']);
                        $questionsCount = count($questions);

                        // Get interviewers count for this position
                        $interviewers = $this->interviewerModel->getInterviewersByPosition($position['id']);
                        $interviewersCount = count($interviewers);

                        // Get actual interview data entries for this position
                        $positionInterviewData = $this->interviewDataModel->getDataByPosition($position['id']);
                        $actualDataCount = count($positionInterviewData);

                        // Calculate expected interview data entries
                        $expectedDataCount = $questionsCount * $interviewersCount * $shortlistedCount;

                        // Calculate progress percentage
                        $progressPercentage = $expectedDataCount > 0 ? ($actualDataCount / $expectedDataCount) * 100 : 0;

                        $positionData = [
                            'id' => $position['id'],
                            'position_no' => $position['position_no'],
                            'designation' => $position['designation'],
                            'classification' => $position['classification'],
                            'award' => $position['award'],
                            'shortlisted_count' => $shortlistedCount,
                            'questions_count' => $questionsCount,
                            'interviewers_count' => $interviewersCount,
                            'actual_data_count' => $actualDataCount,
                            'expected_data_count' => $expectedDataCount,
                            'progress_percentage' => round($progressPercentage, 1),
                            'created_at' => $position['created_at'],
                            'updated_at' => $position['updated_at']
                        ];

                        $groupData['positions'][] = $positionData;
                    }
                }

                // Only add group if it has positions with shortlisted applicants
                if (!empty($groupData['positions'])) {
                    $interviewData[] = $groupData;
                }
            }
        }

        $data['interviewData'] = $interviewData;

        return view('interviews/interview_index', $data);
    }

    /**
     * Open interview for a specific position
     * GET /interviews/open/{position_id}
     */
    public function open($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Open Interview';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->where('for_interview', 1)
            ->where('is_active', 1)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found or not available for interview.');
        }

        // Get position group details
        $positionGroup = $this->positionsGroupModel->find($position['position_group_id']);

        // Get shortlisted applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->orderBy('name', 'ASC')
            ->findAll();

        // Get count of interview questions for this position
        $questionsCount = $this->interviewQuestionModel->where('position_id', $positionId)
            ->countAllResults();

        // Get count of interviewers for this position
        $interviewersCount = $this->interviewerModel->where('position_id', $positionId)
            ->countAllResults();

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->getInterviewersByPosition($positionId);

        // Calculate interview progress for each applicant
        $applicantProgress = [];
        if ($questionsCount > 0 && $interviewersCount > 0) {
            $totalExpectedScores = $questionsCount * $interviewersCount;

            foreach ($applicants as $applicant) {
                // Count how many scores have been entered for this applicant
                $enteredScores = $this->interviewDataModel->where('applicant_id', $applicant['id'])
                    ->where('position_id', $positionId)
                    ->where('is_deleted', false)
                    ->countAllResults();

                $progressPercentage = ($enteredScores / $totalExpectedScores) * 100;

                $applicantProgress[$applicant['id']] = [
                    'entered_scores' => $enteredScores,
                    'total_expected' => $totalExpectedScores,
                    'percentage' => $progressPercentage,
                    'is_complete' => $enteredScores >= $totalExpectedScores
                ];
            }
        }

        // Calculate interviewer status for each applicant
        $interviewerStatus = [];
        if ($questionsCount > 0 && $interviewersCount > 0) {
            foreach ($applicants as $applicant) {
                $interviewerStatus[$applicant['id']] = [];
                foreach ($interviewers as $interviewer) {
                    // Count scores entered by this interviewer for this applicant
                    $enteredCount = $this->interviewDataModel->where('applicant_id', $applicant['id'])
                        ->where('position_id', $positionId)
                        ->where('interviewer_id', $interviewer['id'])
                        ->where('is_deleted', false)
                        ->countAllResults();

                    if ($enteredCount == $questionsCount) {
                        $status = 'full';
                    } elseif ($enteredCount > 0) {
                        $status = 'partial';
                    } else {
                        $status = 'none';
                    }

                    $interviewerStatus[$applicant['id']][] = [
                        'interviewer' => $interviewer,
                        'status' => $status,
                        'entered_count' => $enteredCount,
                        'total_questions' => $questionsCount
                    ];
                }
            }
        }

        $data['position'] = $position;
        $data['positionGroup'] = $positionGroup;
        $data['applicants'] = $applicants;
        $data['questionsCount'] = $questionsCount;
        $data['interviewersCount'] = $interviewersCount;
        $data['applicantProgress'] = $applicantProgress;
        $data['interviewerStatus'] = $interviewerStatus;

        return view('interviews/interview_open', $data);
    }

    /**
     * Display interviewer status overview for all positions
     * GET /interviews/status
     */
    public function interviewerStatus()
    {
        $data['title'] = 'Interviewer Status Overview';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get all positions marked for interview with their position groups
        $positions = $this->positionModel->select('positions.*, positions_groups.group_name, positions_groups.priority')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id')
            ->where('positions.org_id', $orgId)
            ->where('positions.for_interview', 1)
            ->where('positions.is_active', 1)
            ->orderBy('positions_groups.priority', 'ASC')
            ->orderBy('positions.position_no', 'ASC')
            ->findAll();

        $detailedData = [];

        foreach ($positions as $position) {
            // Get shortlisted applicants for this position
            $applicants = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', $orgId)
                ->where('application_status', 'Shortlisted')
                ->orderBy('name', 'ASC')
                ->findAll();

            if (!empty($applicants)) {
                // Get questions and interviewers for this position
                $questions = $this->interviewQuestionModel->getQuestionsByPosition($position['id']);
                $questionsCount = count($questions);
                $interviewers = $this->interviewerModel->getInterviewersByPosition($position['id']);

                foreach ($applicants as $applicant) {
                    // Calculate interviewer status for this applicant
                    $interviewerStatus = [];
                    if ($questionsCount > 0 && !empty($interviewers)) {
                        foreach ($interviewers as $interviewer) {
                            // Count scores entered by this interviewer for this applicant
                            $enteredCount = $this->interviewDataModel->where('applicant_id', $applicant['id'])
                                ->where('position_id', $position['id'])
                                ->where('interviewer_id', $interviewer['id'])
                                ->where('is_deleted', false)
                                ->countAllResults();

                            if ($enteredCount == $questionsCount) {
                                $status = 'full';
                            } elseif ($enteredCount > 0) {
                                $status = 'partial';
                            } else {
                                $status = 'none';
                            }

                            $interviewerStatus[] = [
                                'interviewer' => $interviewer,
                                'status' => $status,
                                'entered_count' => $enteredCount,
                                'total_questions' => $questionsCount
                            ];
                        }
                    }

                    $detailedData[] = [
                        'position_group' => $position['group_name'],
                        'position_no' => $position['position_no'],
                        'position_designation' => $position['designation'],
                        'position_classification' => $position['classification'],
                        'position_award' => $position['award'],
                        'applicant_id' => $applicant['id'],
                        'applicant_name' => $applicant['name'],
                        'applicant_contact' => $applicant['contact_details'],
                        'interviewer_status' => $interviewerStatus,
                        'last_updated' => $applicant['updated_at'],
                        'position_id' => $position['id']
                    ];
                }
            }
        }

        $data['detailedData'] = $detailedData;

        return view('interviews/interview_status', $data);
    }

    // ==================== INTERVIEW QUESTIONS CRUD ====================

    /**
     * Display interview questions for a position
     * GET /interviews/questions/{position_id}
     */
    public function questions($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Interview Questions';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get questions for this position
        $questions = $this->interviewQuestionModel->getQuestionsByPosition($positionId);

        $data['position'] = $position;
        $data['questions'] = $questions;
        $data['positionId'] = $positionId;

        return view('interviews/interview_questions', $data);
    }

    /**
     * Show form to create new interview question
     * GET /interviews/questions/create/{position_id}
     */
    public function createQuestion($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Create Interview Question';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_question_create', $data);
    }

    /**
     * Store new interview question
     * POST /interviews/questions/store/{position_id}
     */
    public function storeQuestion($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get next question number
        $lastQuestion = $this->interviewQuestionModel->where('position_id', $positionId)
            ->where('exercise_id', get_current_exercise_id())
            ->orderBy('question_no', 'DESC')
            ->first();
        $nextQuestionNo = ($lastQuestion['question_no'] ?? 0) + 1;

        $data = [
            'exercise_id' => get_current_exercise_id(),
            'position_id' => $positionId,
            'question_no' => $nextQuestionNo,
            'question_text' => $this->request->getPost('question_text'),
            'set_score' => $this->request->getPost('set_score') ?: 0,
            'created_by' => session('user_id')
        ];

        if ($this->interviewQuestionModel->insert($data)) {
            return redirect()->to('interviews/questions/' . $positionId)
                ->with('success', 'Interview question created successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create interview question.');
        }
    }

    /**
     * Show form to edit interview question
     * GET /interviews/questions/edit/{question_id}
     */
    public function editQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        $data['title'] = 'Edit Interview Question';
        $data['menu'] = 'interviews';

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        // Get position details
        $position = $this->positionModel->find($question['position_id']);

        $data['question'] = $question;
        $data['position'] = $position;

        return view('interviews/interview_question_edit', $data);
    }

    /**
     * Update interview question
     * POST /interviews/questions/update/{question_id}
     */
    public function updateQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        $data = [
            'question_text' => $this->request->getPost('question_text'),
            'set_score' => $this->request->getPost('set_score') ?: 0,
            'updated_by' => session('user_id')
        ];

        if ($this->interviewQuestionModel->update($questionId, $data)) {
            return redirect()->to('interviews/questions/' . $question['position_id'])
                ->with('success', 'Interview question updated successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update interview question.');
        }
    }

    /**
     * Delete interview question
     * POST /interviews/questions/delete/{question_id}
     */
    public function deleteQuestion($questionId = null)
    {
        if (!$questionId) {
            return redirect()->to('interviews')->with('error', 'Question ID is required.');
        }

        // Get question details
        $question = $this->interviewQuestionModel->find($questionId);

        if (!$question) {
            return redirect()->to('interviews')->with('error', 'Question not found.');
        }

        if ($this->interviewQuestionModel->delete($questionId)) {
            return redirect()->to('interviews/questions/' . $question['position_id'])
                ->with('success', 'Interview question deleted successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to delete interview question.');
        }
    }

    /**
     * Show import questions form
     * GET /interviews/questions/import/{position_id}
     */
    public function importQuestions($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['title'] = 'Import Interview Questions';
        $data['menu'] = 'interviews';
        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_questions_import', $data);
    }

    /**
     * Process import questions from CSV
     * POST /interviews/questions/import/{position_id}
     */
    public function processImportQuestions($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $file = $this->request->getFile('csv_file');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return redirect()->back()
                ->with('error', 'Please select a valid CSV file.');
        }

        if ($file->getExtension() !== 'csv') {
            return redirect()->back()
                ->with('error', 'Please upload a CSV file only.');
        }

        try {
            // Move uploaded file
            $newName = $file->getRandomName();
            $file->move(WRITEPATH . 'uploads', $newName);

            // Read CSV data
            $csvData = array_map('str_getcsv', file(WRITEPATH . 'uploads/' . $newName));

            // Remove header row
            $header = array_shift($csvData);

            // Validate CSV header
            if (count($header) < 2 ||
                strtolower(trim($header[0])) !== 'question' ||
                strtolower(trim($header[1])) !== 'max score') {
                return redirect()->back()
                    ->with('error', 'Invalid CSV format. Expected columns: question, max score');
            }

            // Get next question number
            $lastQuestion = $this->interviewQuestionModel->where('position_id', $positionId)
                ->orderBy('question_no', 'DESC')
                ->first();
            $nextQuestionNo = ($lastQuestion['question_no'] ?? 0) + 1;

            $importedCount = 0;
            $skippedCount = 0;
            $errors = [];

            foreach ($csvData as $rowIndex => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Validate row has required columns
                if (count($row) < 2) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Missing required columns";
                    $skippedCount++;
                    continue;
                }

                $questionText = trim($row[0]);
                $maxScore = trim($row[1]);

                // Validate question text
                if (empty($questionText)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Question text is required";
                    $skippedCount++;
                    continue;
                }

                // Validate max score
                if (!is_numeric($maxScore) || $maxScore < 0) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Max score must be a valid number";
                    $skippedCount++;
                    continue;
                }

                // Check for duplicate question
                $existingQuestion = $this->interviewQuestionModel->where([
                    'exercise_id' => get_current_exercise_id(),
                    'position_id' => $positionId,
                    'question_text' => $questionText
                ])->first();

                if ($existingQuestion) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Question already exists";
                    $skippedCount++;
                    continue;
                }

                // Insert question
                $questionData = [
                    'exercise_id' => get_current_exercise_id(),
                    'position_id' => $positionId,
                    'question_no' => $nextQuestionNo,
                    'question_text' => $questionText,
                    'set_score' => floatval($maxScore),
                    'created_by' => session('user_id')
                ];

                if ($this->interviewQuestionModel->insert($questionData)) {
                    $importedCount++;
                    $nextQuestionNo++;
                } else {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Failed to save question";
                    $skippedCount++;
                }
            }

            // Clean up uploaded file
            unlink(WRITEPATH . 'uploads/' . $newName);

            // Prepare success message
            $message = "Import completed. Imported: $importedCount questions";
            if ($skippedCount > 0) {
                $message .= ", Skipped: $skippedCount questions";
            }

            // Add errors to session if any
            if (!empty($errors)) {
                session()->setFlashdata('import_errors', $errors);
            }

            return redirect()->to('interviews/questions/' . $positionId)
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error processing CSV file: ' . $e->getMessage());
        }
    }

    /**
     * Download CSV template for questions import
     * GET /interviews/questions/template/{position_id}
     */
    public function downloadTemplate($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Create CSV content
        $csvContent = "question,max score\n";
        $csvContent .= "\"What are your strengths?\",10\n";
        $csvContent .= "\"Describe your experience with teamwork\",15\n";
        $csvContent .= "\"How do you handle pressure?\",10\n";
        $csvContent .= "\"Where do you see yourself in 5 years?\",5\n";

        // Set headers for download
        $filename = 'interview_questions_template_' . date('Y-m-d') . '.csv';

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($csvContent);
    }

    // ==================== INTERVIEWERS CRUD ====================

    /**
     * Display interviewers for a position
     * GET /interviews/interviewers/{position_id}
     */
    public function interviewers($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Interviewers';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->getInterviewersByPosition($positionId);

        $data['position'] = $position;
        $data['interviewers'] = $interviewers;
        $data['positionId'] = $positionId;

        return view('interviews/interview_interviewers', $data);
    }

    /**
     * Show form to create new interviewer
     * GET /interviews/interviewers/create/{position_id}
     */
    public function createInterviewer($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $data['title'] = 'Add Interviewer';
        $data['menu'] = 'interviews';
        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_interviewer_create', $data);
    }

    /**
     * Store new interviewer
     * POST /interviews/interviewers/store/{position_id}
     */
    public function storeInterviewer($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('exercise_id', get_current_exercise_id())
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data = [
            'exercise_id' => get_current_exercise_id(),
            'position_id' => $positionId,
            'interviewer_name' => $this->request->getPost('interviewer_name'),
            'interviewer_position' => $this->request->getPost('interviewer_position'),
            'created_by' => session('user_id')
        ];

        if ($this->interviewerModel->insert($data)) {
            return redirect()->to('interviews/interviewers/' . $positionId)
                ->with('success', 'Interviewer added successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to add interviewer.');
        }
    }

    /**
     * Show form to edit interviewer
     * GET /interviews/interviewers/edit/{interviewer_id}
     */
    public function editInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        $data['title'] = 'Edit Interviewer';
        $data['menu'] = 'interviews';

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        // Get position details
        $position = $this->positionModel->find($interviewer['position_id']);

        $data['interviewer'] = $interviewer;
        $data['position'] = $position;

        return view('interviews/interview_interviewer_edit', $data);
    }

    /**
     * Update interviewer
     * POST /interviews/interviewers/update/{interviewer_id}
     */
    public function updateInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        $data = [
            'interviewer_name' => $this->request->getPost('interviewer_name'),
            'interviewer_position' => $this->request->getPost('interviewer_position'),
            'updated_by' => session('user_id')
        ];

        if ($this->interviewerModel->update($interviewerId, $data)) {
            return redirect()->to('interviews/interviewers/' . $interviewer['position_id'])
                ->with('success', 'Interviewer updated successfully.');
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update interviewer.');
        }
    }

    /**
     * Delete interviewer
     * POST /interviews/interviewers/delete/{interviewer_id}
     */
    public function deleteInterviewer($interviewerId = null)
    {
        if (!$interviewerId) {
            return redirect()->to('interviews')->with('error', 'Interviewer ID is required.');
        }

        // Get interviewer details
        $interviewer = $this->interviewerModel->find($interviewerId);

        if (!$interviewer) {
            return redirect()->to('interviews')->with('error', 'Interviewer not found.');
        }

        if ($this->interviewerModel->delete($interviewerId)) {
            return redirect()->to('interviews/interviewers/' . $interviewer['position_id'])
                ->with('success', 'Interviewer deleted successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to delete interviewer.');
        }
    }

    // ==================== INTERVIEWERS IMPORT ====================

    /**
     * Show import interviewers form
     * GET /interviews/interviewers/import/{position_id}
     */
    public function importInterviewers($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Get position details
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data['title'] = 'Import Interviewers';
        $data['menu'] = 'interviews';
        $data['position'] = $position;
        $data['positionId'] = $positionId;

        return view('interviews/interview_interviewers_import', $data);
    }

    /**
     * Process import interviewers from CSV
     * POST /interviews/interviewers/import/{position_id}
     */
    public function processImportInterviewers($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $file = $this->request->getFile('csv_file');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return redirect()->back()
                ->with('error', 'Please select a valid CSV file.');
        }

        if ($file->getExtension() !== 'csv') {
            return redirect()->back()
                ->with('error', 'Please upload a CSV file only.');
        }

        try {
            // Move uploaded file
            $newName = $file->getRandomName();
            $file->move(WRITEPATH . 'uploads', $newName);

            // Read CSV data
            $csvData = array_map('str_getcsv', file(WRITEPATH . 'uploads/' . $newName));

            // Remove header row
            $header = array_shift($csvData);

            // Validate CSV header
            if (count($header) < 2 ||
                strtolower(trim($header[0])) !== 'interviewer_name' ||
                strtolower(trim($header[1])) !== 'interviewer_position') {
                return redirect()->back()
                    ->with('error', 'Invalid CSV format. Expected columns: interviewer_name, interviewer_position');
            }

            $importedCount = 0;
            $skippedCount = 0;
            $errors = [];

            foreach ($csvData as $rowIndex => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Validate row has required columns
                if (count($row) < 2) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Missing required columns";
                    $skippedCount++;
                    continue;
                }

                $interviewerName = trim($row[0]);
                $interviewerPosition = trim($row[1]);

                // Validate required fields
                if (empty($interviewerName)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Interviewer name is required";
                    $skippedCount++;
                    continue;
                }

                // Check for duplicate interviewer name in the same position
                $existingInterviewer = $this->interviewerModel->where([
                    'position_id' => $positionId,
                    'interviewer_name' => $interviewerName
                ])->first();

                if ($existingInterviewer) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Interviewer '" . $interviewerName . "' already exists";
                    $skippedCount++;
                    continue;
                }

                // Insert interviewer
                $interviewerData = [
                    'position_id' => $positionId,
                    'interviewer_name' => $interviewerName,
                    'interviewer_position' => $interviewerPosition,
                    'created_by' => session('user_id')
                ];

                if ($this->interviewerModel->insert($interviewerData)) {
                    $importedCount++;
                } else {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Failed to save interviewer";
                    $skippedCount++;
                }
            }

            // Clean up uploaded file
            unlink(WRITEPATH . 'uploads/' . $newName);

            // Prepare success message
            $message = "Import completed. Imported: $importedCount interviewers";
            if ($skippedCount > 0) {
                $message .= ", Skipped: $skippedCount interviewers";
            }

            // Add errors to session if any
            if (!empty($errors)) {
                session()->setFlashdata('import_errors', $errors);
            }

            return redirect()->to('interviews/interviewers/' . $positionId)
                ->with('success', $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error processing CSV file: ' . $e->getMessage());
        }
    }

    /**
     * Download CSV template for interviewers import
     * GET /interviews/interviewers/template/{position_id}
     */
    public function downloadInterviewersTemplate($positionId = null)
    {
        if (!$positionId) {
            return redirect()->to('interviews')->with('error', 'Position ID is required.');
        }

        $orgId = session('org_id');

        // Validate position exists
        $position = $this->positionModel->where('id', $positionId)
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Create CSV content
        $csvContent = "interviewer_name,interviewer_position\n";
        $csvContent .= "\"Dr. John Smith\",\"Senior Manager\"\n";
        $csvContent .= "\"Ms. Sarah Johnson\",\"HR Director\"\n";
        $csvContent .= "\"Prof. Michael Brown\",\"Technical Lead\"\n";
        $csvContent .= "\"Mrs. Emily Davis\",\"Department Head\"\n";

        // Set headers for download
        $filename = 'interviewers_template_' . date('Y-m-d') . '.csv';

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($csvContent);
    }

    // ==================== INTERVIEW DATA CRUD ====================

    /**
     * View interview data for an applicant
     * GET /interviews/data/view/{applicant_id}
     */
    public function viewInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        // Get position details
        $position = $this->positionModel->find($applicant['position_id']);

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get interview questions for this position
        $questions = $this->interviewQuestionModel->getQuestionsByPosition($applicant['position_id']);

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->getInterviewersByPosition($applicant['position_id']);

        // Get existing interview data for this applicant
        $existingData = $this->interviewDataModel->where('applicant_id', $applicantId)
            ->where('position_id', $applicant['position_id'])
            ->where('is_deleted', false)
            ->findAll();

        // Organize existing data by interviewer and question
        $scores = [];
        foreach ($existingData as $data) {
            $scores[$data['interviewer_id']][$data['question_id']] = [
                'score' => $data['score'],
                'comments' => $data['comments']
            ];
        }

        $data['title'] = 'Interview Data - ' . $applicant['name'];
        $data['menu'] = 'interviews';
        $data['applicant'] = $applicant;
        $data['position'] = $position;
        $data['questions'] = $questions;
        $data['interviewers'] = $interviewers;
        $data['scores'] = $scores;

        return view('interviews/interview_data_view', $data);
    }

    /**
     * Store interview data scores
     * POST /interviews/data/store/{applicant_id}
     */
    public function storeInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        $scores = $this->request->getPost('scores');
        $comments = $this->request->getPost('comments');

        if (empty($scores)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'No scores provided.');
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($scores as $interviewerId => $questionScores) {
            foreach ($questionScores as $questionId => $score) {
                // Check if data already exists
                $existingData = $this->interviewDataModel->where([
                    'applicant_id' => $applicantId,
                    'position_id' => $applicant['position_id'],
                    'interviewer_id' => $interviewerId,
                    'question_id' => $questionId,
                    'is_deleted' => false
                ])->first();

                // Skip if score is empty or null
                if ($score === '' || $score === null) {
                    // If there's existing data and score is now empty, delete it
                    if ($existingData) {
                        if ($this->interviewDataModel->delete($existingData['id'])) {
                            $successCount++;
                        } else {
                            $errorCount++;
                        }
                    }
                    continue;
                }

                $dataToSave = [
                    'position_id' => $applicant['position_id'],
                    'interviewer_id' => $interviewerId,
                    'applicant_id' => $applicantId,
                    'question_id' => $questionId,
                    'score' => floatval($score),
                    'comments' => isset($comments[$interviewerId][$questionId]) ? $comments[$interviewerId][$questionId] : '',
                    'created_by' => session('user_id'),
                    'updated_by' => session('user_id')
                ];

                if ($existingData) {
                    // Update existing data
                    unset($dataToSave['created_by']);
                    if ($this->interviewDataModel->update($existingData['id'], $dataToSave)) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                } else {
                    // Insert new data
                    if ($this->interviewDataModel->insert($dataToSave)) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                }
            }
        }

        if ($successCount > 0 && $errorCount == 0) {
            return redirect()->to('interviews/data/view/' . $applicantId)
                ->with('success', 'Interview scores saved successfully.');
        } elseif ($successCount > 0 && $errorCount > 0) {
            return redirect()->to('interviews/data/view/' . $applicantId)
                ->with('warning', "Saved {$successCount} scores, but {$errorCount} failed to save.");
        } else {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to save interview scores.');
        }
    }

    // ==================== INTERVIEW DATA IMPORT ====================

    /**
     * Show import interview data form
     * GET /interviews/data/import/{applicant_id}
     */
    public function importInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        // Get position details
        $position = $this->positionModel->where('id', $applicant['position_id'])
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        $data = [
            'title' => 'Import Interview Data',
            'applicant' => $applicant,
            'position' => $position
        ];

        return view('interviews/interview_data_import', $data);
    }

    /**
     * Process import interview data CSV
     * POST /interviews/data/import/{applicant_id}
     */
    public function processImportInterviewData($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        // Get position details
        $position = $this->positionModel->where('id', $applicant['position_id'])
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Validate file upload
        $file = $this->request->getFile('csv_file');

        if (!$file || !$file->isValid()) {
            return redirect()->back()
                ->with('error', 'Please select a valid CSV file.');
        }

        if ($file->getExtension() !== 'csv') {
            return redirect()->back()
                ->with('error', 'Please upload a CSV file only.');
        }

        try {
            // Move uploaded file
            $newName = $file->getRandomName();
            $file->move(WRITEPATH . 'uploads', $newName);

            // Read CSV data
            $csvData = array_map('str_getcsv', file(WRITEPATH . 'uploads/' . $newName));

            // Remove header row
            $header = array_shift($csvData);

            // Validate CSV header
            if (count($header) < 4 ||
                strtolower(trim($header[0])) !== 'interviewer_name' ||
                strtolower(trim($header[1])) !== 'question_no' ||
                strtolower(trim($header[2])) !== 'score' ||
                strtolower(trim($header[3])) !== 'comments') {
                return redirect()->back()
                    ->with('error', 'Invalid CSV format. Expected columns: interviewer_name, question_no, score, comments');
            }

            $importedCount = 0;
            $skippedCount = 0;
            $errors = [];

            foreach ($csvData as $rowIndex => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Validate row has required columns
                if (count($row) < 4) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Insufficient columns";
                    $skippedCount++;
                    continue;
                }

                $interviewerName = trim($row[0]);
                $questionNo = trim($row[1]);
                $score = trim($row[2]);
                $comments = trim($row[3]);

                // Validate required fields
                if (empty($interviewerName)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Interviewer name is required";
                    $skippedCount++;
                    continue;
                }

                if (empty($questionNo) || !is_numeric($questionNo)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Valid question number is required";
                    $skippedCount++;
                    continue;
                }

                // Find interviewer
                $interviewer = $this->interviewerModel->where([
                    'position_id' => $position['id'],
                    'interviewer_name' => $interviewerName,
                    'is_deleted' => 0
                ])->first();

                if (!$interviewer) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Interviewer '{$interviewerName}' not found";
                    $skippedCount++;
                    continue;
                }

                // Find question
                $question = $this->interviewQuestionModel->where([
                    'position_id' => $position['id'],
                    'question_no' => intval($questionNo),
                    'is_deleted' => 0
                ])->first();

                if (!$question) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Question number '{$questionNo}' not found";
                    $skippedCount++;
                    continue;
                }

                // Skip row if score is empty
                if (empty($score)) {
                    $skippedCount++;
                    continue;
                }

                // Validate score
                if (!is_numeric($score)) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Score must be numeric";
                    $skippedCount++;
                    continue;
                }

                $scoreValue = floatval($score);
                if ($scoreValue < 0 || $scoreValue > $question['set_score']) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Score must be between 0 and {$question['set_score']}";
                    $skippedCount++;
                    continue;
                }

                // Check if data already exists
                $existingData = $this->interviewDataModel->where([
                    'position_id' => $position['id'],
                    'interviewer_id' => $interviewer['id'],
                    'applicant_id' => $applicantId,
                    'question_id' => $question['id'],
                    'is_deleted' => 0
                ])->first();

                $dataToSave = [
                    'position_id' => $position['id'],
                    'interviewer_id' => $interviewer['id'],
                    'applicant_id' => $applicantId,
                    'question_id' => $question['id'],
                    'score' => $scoreValue,
                    'comments' => $comments,
                    'created_by' => session('user_id'),
                    'updated_by' => session('user_id')
                ];

                if ($existingData) {
                    // Update existing data
                    unset($dataToSave['created_by']);
                    if ($this->interviewDataModel->update($existingData['id'], $dataToSave)) {
                        $importedCount++;
                    } else {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Failed to update data";
                        $skippedCount++;
                    }
                } else {
                    // Insert new data
                    if ($this->interviewDataModel->insert($dataToSave)) {
                        $importedCount++;
                    } else {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Failed to save data";
                        $skippedCount++;
                    }
                }
            }

            // Clean up uploaded file
            unlink(WRITEPATH . 'uploads/' . $newName);

            // Prepare result message
            $message = "Import completed: {$importedCount} records imported";
            if ($skippedCount > 0) {
                $message .= ", {$skippedCount} records skipped";
            }

            if (!empty($errors)) {
                $message .= ". Errors: " . implode('; ', array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $message .= " and " . (count($errors) - 5) . " more...";
                }
            }

            $alertType = $importedCount > 0 ? ($skippedCount > 0 ? 'warning' : 'success') : 'error';

            return redirect()->to('interviews/data/view/' . $applicantId)
                ->with($alertType, $message);

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error processing CSV file: ' . $e->getMessage());
        }
    }

    /**
     * Download interview data template CSV
     * GET /interviews/data/template/{applicant_id}
     */
    public function downloadInterviewDataTemplate($applicantId = null)
    {
        if (!$applicantId) {
            return redirect()->to('interviews')->with('error', 'Applicant ID is required.');
        }

        $orgId = session('org_id');

        // Get applicant details
        $applicant = $this->applicantModel->where('id', $applicantId)
            ->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->first();

        if (!$applicant) {
            return redirect()->to('interviews')->with('error', 'Applicant not found or not shortlisted.');
        }

        // Get position details
        $position = $this->positionModel->where('id', $applicant['position_id'])
            ->where('org_id', $orgId)
            ->first();

        if (!$position) {
            return redirect()->to('interviews')->with('error', 'Position not found.');
        }

        // Get interviewers for this position
        $interviewers = $this->interviewerModel->where('position_id', $position['id'])
            ->where('is_deleted', 0)
            ->findAll();

        // Get questions for this position
        $questions = $this->interviewQuestionModel->where('position_id', $position['id'])
            ->where('is_deleted', 0)
            ->orderBy('question_no', 'ASC')
            ->findAll();

        if (empty($interviewers) || empty($questions)) {
            return redirect()->back()
                ->with('error', 'No interviewers or questions found for this position.');
        }

        // Create CSV content
        $csvContent = "interviewer_name,question_no,score,comments\n";

        // Generate all combinations of interviewers and questions
        foreach ($interviewers as $interviewer) {
            foreach ($questions as $question) {
                $csvContent .= '"' . $interviewer['interviewer_name'] . '",' .
                              $question['question_no'] . ',' .
                              ',' . // Empty score
                              "\n"; // Empty comments
            }
        }

        // Set headers for download
        $filename = 'interview_data_template_' . $applicant['name'] . '_' . date('Y-m-d') . '.csv';
        $filename = preg_replace('/[^a-zA-Z0-9_\-\.]/', '_', $filename); // Clean filename

        return $this->response
            ->setHeader('Content-Type', 'text/csv')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($csvContent);
    }
}
