<?php

namespace App\Controllers;

use App\Models\DakoiiOrgModel;

class Settings extends BaseController
{
    protected $dakoiiOrgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'exercise', 'exercise_settings']);
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }

    public function index()
    {
        $data['title'] = 'Settings';
        $data['menu'] = 'settings';

        $orgId = session('org_id');
        $data['org'] = $this->dakoiiOrgModel->find($orgId);

        return view('settings/index', $data);
    }

    /**
     * Handle advertisement settings update
     */
    public function settings_advertisement()
    {
        $orgId = session('org_id');

        $rules = [
            'advertisement_no' => 'permit_empty',
            'advertisement_date' => 'permit_empty',
            'mode_of_advert' => 'permit_empty',
            'sort_out_of' => 'permit_empty|integer',
        ];

        if ($this->validate($rules)) {
            $updateData = [
                'advertisement_no' => $this->request->getPost('advertisement_no'),
                'advertisement_date' => $this->request->getPost('advertisement_date'),
                'mode_of_advert' => $this->request->getPost('mode_of_advert'),
                'sort_out_of' => $this->request->getPost('sort_out_of'),
            ];

            $this->dakoiiOrgModel->update($orgId, $updateData);
            return redirect()->back()->with('success', 'Advertisement settings updated successfully.');
        }

        return redirect()->back()->with('error', 'Invalid input data.');
    }

    /**
     * Handle organization logo update
     */
    public function settings_logo()
    {
        $orgId = session('org_id');
        $logo = $this->request->getFile('logo');

        if ($logo && $logo->isValid() && !$logo->hasMoved()) {
            $newName = $logo->getRandomName();
            if ($logo->move(FCPATH . 'public/uploads/org_logo', $newName)) {
                $updateData = [
                    'org_logo' => 'public/uploads/org_logo/'.$newName
                ];

                $this->dakoiiOrgModel->update($orgId, $updateData);
                return redirect()->back()->with('success', 'Logo updated successfully.');
            }
            return redirect()->back()->with('error', 'Failed to move the uploaded file.');
        }

        return redirect()->back()->with('error', 'Please select a valid image file.');
    }

    /**
     * Handle report settings update
     */
    public function settings_report()
    {
        $orgId = session('org_id');

        $updateData = [
            'introduction' => $this->request->getPost('introduction'),
            'composition' => $this->request->getPost('composition'),
            'criteria' => $this->request->getPost('criteria'),
            'culling' => $this->request->getPost('culling'),
        ];

        $this->dakoiiOrgModel->update($orgId, $updateData);
        return redirect()->back()->with('success', 'Report settings updated successfully.');
    }

    /**
     * Display interview settings page
     */
    public function interviews()
    {
        // Ensure exercise context exists
        helper(['exercise', 'exercise_settings']);
        require_exercise_context();

        $data['title'] = 'Interview Settings';
        $data['menu'] = 'settings';

        // Get current exercise info for display
        $data['current_exercise'] = get_current_exercise();

        // Get interview settings from exercise settings (not organization)
        $interviewSettings = get_interview_settings();

        // If no settings exist, try to migrate from organization settings
        if (empty($interviewSettings)) {
            $migrated = migrate_org_settings_to_exercise();
            if ($migrated > 0) {
                $interviewSettings = get_interview_settings();
            }
        }

        // Ensure interviewSettings is an array
        if (!is_array($interviewSettings)) {
            $interviewSettings = json_decode($interviewSettings ?? '{}', true) ?: [];
        }

        // Set default values if not set
        $data['settings'] = [
            'minutes_per_interview' => $interviewSettings['minutes_per_interview'] ?? 30,
            'transition_time' => $interviewSettings['transition_time'] ?? 5,
            'start_time' => $interviewSettings['start_time'] ?? '08:00',
            'end_time' => $interviewSettings['end_time'] ?? '16:00',
            'start_date' => $interviewSettings['start_date'] ?? date('Y-m-d'),
            'break_times' => $interviewSettings['break_times'] ?? [],
            'break_days' => $interviewSettings['break_days'] ?? [],
            'interview_venue' => $interviewSettings['interview_venue'] ?? '',
            'interview_contacts' => $interviewSettings['interview_contacts'] ?? '',
            'reply_email' => $interviewSettings['reply_email'] ?? '',
            'additional_messages' => $interviewSettings['additional_messages'] ?? '',
            'sign_off_signature' => $interviewSettings['sign_off_signature'] ?? ''
        ];

        return view('settings/interviews', $data);
    }

    /**
     * Handle interview settings update
     */
    public function save_interview_settings()
    {
        // Ensure exercise context exists
        helper(['exercise', 'exercise_settings']);
        require_exercise_context();

        // Get all form data
        $minutes_per_interview = $this->request->getPost('minutes_per_interview');
        $transition_time = $this->request->getPost('transition_time');
        $start_time = $this->request->getPost('start_time');
        $end_time = $this->request->getPost('end_time');
        $start_date = $this->request->getPost('start_date');

        // Get break times and break days arrays
        $break_start_times = $this->request->getPost('break_start_time') ?? [];
        $break_end_times = $this->request->getPost('break_end_time') ?? [];
        $break_days = $this->request->getPost('break_days') ?? [];

        // Format break times array
        $break_times = [];
        foreach ($break_start_times as $index => $start) {
            if (isset($break_end_times[$index]) && !empty($start) && !empty($break_end_times[$index])) {
                $break_times[] = [
                    'start' => $start,
                    'end' => $break_end_times[$index]
                ];
            }
        }

        // Get new communication fields
        $interview_venue = $this->request->getPost('interview_venue');
        $interview_contacts = $this->request->getPost('interview_contacts');
        $reply_email = $this->request->getPost('reply_email');
        $additional_messages = $this->request->getPost('additional_messages');
        $sign_off_signature = $this->request->getPost('sign_off_signature');

        // Combine all settings in a JSON structure
        $interviewSettings = [
            'minutes_per_interview' => (int)$minutes_per_interview,
            'transition_time' => (int)$transition_time,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'start_date' => $start_date,
            'break_times' => $break_times,
            'break_days' => $break_days,
            'interview_venue' => $interview_venue,
            'interview_contacts' => $interview_contacts,
            'reply_email' => $reply_email,
            'additional_messages' => $additional_messages,
            'sign_off_signature' => $sign_off_signature
        ];

        // Save settings to exercise settings (not organization)
        $success = set_interview_settings($interviewSettings);

        if ($success) {
            return redirect()->to('settings/interviews')->with('success', 'Interview settings saved successfully for current exercise.');
        } else {
            return redirect()->to('settings/interviews')->with('error', 'Failed to save interview settings. Please try again.');
        }
    }

    /**
     * Display pre-selection report editor page
     */
    public function pre_selection_report()
    {
        $data['title'] = 'Pre-Selection Report';
        $data['menu'] = 'settings';

        $orgId = session('org_id');
        $data['org'] = $this->dakoiiOrgModel->find($orgId);

        return view('settings/pre_selection_report', $data);
    }

    /**
     * Handle AJAX save for pre-selection report content
     */
    public function save_pre_selection_report()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $orgId = session('org_id');
        $culling = $this->request->getPost('culling');

        if ($this->dakoiiOrgModel->update($orgId, ['culling' => $culling])) {
            return $this->response->setJSON(['success' => true]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to save']);
    }

    /**
     * Handle AJAX save for committee signatures
     */
    public function save_signatures()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $orgId = session('org_id');
        $composition = $this->request->getPost('composition');

        if ($this->dakoiiOrgModel->update($orgId, ['composition' => $composition])) {
            return $this->response->setJSON(['success' => true]);
        }

        return $this->response->setJSON(['success' => false, 'message' => 'Failed to save']);
    }
}
