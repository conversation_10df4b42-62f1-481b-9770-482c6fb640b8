<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Interview Schedule
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('reports') ?>">Reports</a></li>
                        <li class="breadcrumb-item active">Interview Schedule</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">


            <?php if (empty($scheduleData)): ?>
                <div class="alert alert-info">
                    <h5><i class="icon fas fa-info-circle"></i> No Schedule Available</h5>
                    <p>There are no shortlisted applicants for positions marked for interview. Please shortlist applicants and ensure positions are marked for interview.</p>
                </div>
            <?php else: ?>
                <!-- Schedule Overview Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>
                            Schedule Overview
                        </h3>
                        <div class="card-tools">
                            <?php if (isset($current_exercise)): ?>
                            <span class="badge badge-info mr-2">
                                <i class="fas fa-dumbbell mr-1"></i>
                                Exercise: <?= esc($current_exercise['exercise_title']) ?>
                            </span>
                            <?php endif; ?>
                            <button type="button" class="btn btn-tool" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Schedule
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-box bg-info">
                                    <span class="info-box-icon"><i class="fas fa-calendar"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Interview Period</span>
                                        <span class="info-box-number">
                                            <?= date('d M Y', strtotime($startDate)) ?> - <?= date('d M Y', strtotime($endDate)) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box bg-success">
                                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Interviewees</span>
                                        <span class="info-box-number"><?= $totalApplicants ?> Applicants</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-box bg-warning">
                                    <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Interview Duration</span>
                                        <span class="info-box-number">
                                            <?= $interviewSettings['minutes_per_interview'] ?> minutes per interview
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card card-outline card-secondary">
                                    <div class="card-header">
                                        <h3 class="card-title">Daily Schedule</h3>
                                    </div>
                                    <div class="card-body p-2">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <span><i class="far fa-clock mr-2"></i>Interview Hours</span>
                                                <span class="badge badge-info"><?= date('h:i A', strtotime($interviewSettings['start_time'])) ?> - <?= date('h:i A', strtotime($interviewSettings['end_time'])) ?></span>
                                            </li>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <span><i class="fas fa-exchange-alt mr-2"></i>Transition Time</span>
                                                <span class="badge badge-info"><?= $interviewSettings['transition_time'] ?> minutes</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card card-outline card-secondary">
                                    <div class="card-header">
                                        <h3 class="card-title">Break Times</h3>
                                    </div>
                                    <div class="card-body p-2">
                                        <?php if (empty($interviewSettings['break_times'])): ?>
                                            <p class="text-muted mb-0">No break times scheduled</p>
                                        <?php else: ?>
                                            <ul class="list-group list-group-flush">
                                                <?php foreach ($interviewSettings['break_times'] as $break): ?>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <span><i class="fas fa-coffee mr-2"></i>Break</span>
                                                        <span class="badge badge-warning">
                                                            <?= date('h:i A', strtotime($break['start'])) ?> - <?= date('h:i A', strtotime($break['end'])) ?>
                                                        </span>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Interview Communication Information -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card card-outline card-info">
                                    <div class="card-header">
                                        <h3 class="card-title">
                                            <i class="fas fa-envelope mr-2"></i>
                                            Interview Communication Information
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <?php if (!empty($interviewSettings['interview_venue'])): ?>
                                                <div class="info-box">
                                                    <span class="info-box-icon bg-info"><i class="fas fa-map-marker-alt"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Interview Venue</span>
                                                        <span class="info-box-number"><?= esc($interviewSettings['interview_venue']) ?></span>
                                                    </div>
                                                </div>
                                                <?php endif; ?>

                                                <?php if (!empty($interviewSettings['reply_email'])): ?>
                                                <div class="info-box">
                                                    <span class="info-box-icon bg-success"><i class="fas fa-reply"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Reply to Email</span>
                                                        <span class="info-box-number"><?= esc($interviewSettings['reply_email']) ?></span>
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="col-md-6">
                                                <?php if (!empty($interviewSettings['interview_contacts'])): ?>
                                                <div class="info-box">
                                                    <span class="info-box-icon bg-warning"><i class="fas fa-phone"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Interview Contacts</span>
                                                        <div class="mt-2"><?= nl2br(esc($interviewSettings['interview_contacts'])) ?></div>
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="row mt-2">
                                            <?php if (!empty($interviewSettings['additional_messages'])): ?>
                                            <div class="col-md-12">
                                                <div class="callout callout-info">
                                                    <h5><i class="fas fa-info-circle mr-2"></i>Additional Information</h5>
                                                    <div><?= nl2br(esc($interviewSettings['additional_messages'])) ?></div>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (!empty($interviewSettings['sign_off_signature'])): ?>
                                            <div class="col-md-12">
                                                <div class="callout callout-success">
                                                    <h5><i class="fas fa-signature mr-2"></i>Sign-off Signature</h5>
                                                    <div><?= nl2br(esc($interviewSettings['sign_off_signature'])) ?></div>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Schedule Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list mr-2"></i>
                            Detailed Interview Schedule
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-success btn-sm mr-2" id="printExcelBtn">
                                <i class="fas fa-file-excel mr-1"></i> Print to Excel
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" id="printTableBtn">
                                <i class="fas fa-print mr-1"></i> Print to PDF
                            </button>
                        </div>
                        <div class="float-right">
                            <button type="button" class="btn btn-primary btn-sm" id="sendNotificationsBtn">
                                <i class="fas fa-envelope mr-1"></i> Send Notifications
                            </button>
                            <button type="button" class="btn btn-success btn-sm ml-2 export-excel">
                                <i class="fas fa-file-excel mr-1"></i> Export to Excel
                            </button>
                            <button type="button" class="btn btn-danger btn-sm ml-2 export-pdf">
                                <i class="fas fa-file-pdf mr-1"></i> Export to PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" id="schedule-table">
                                <thead>
                                    <tr class="bg-primary">
                                        <th>#</th>
                                        <th>Date</th>
                                        <th>Time</th>
                                        <th>Position</th>
                                        <th>Applicant</th>
                                        <th width='10%'>Contacts</th>
                                        <th>Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $currentDisplayDate = '';
                                    $currentPositionGroup = '';
                                    $applicantCount = 0;
                                    foreach ($sortedApplicants as $applicant):
                                        $applicantDate = $applicant['date'];
                                        $isNewDate = $currentDisplayDate !== $applicantDate;
                                        $currentDisplayDate = $applicantDate;

                                        // Check if this is a new position group
                                        $isNewPositionGroup = $currentPositionGroup !== $applicant['group_name'];
                                        if ($isNewPositionGroup) {
                                            $currentPositionGroup = $applicant['group_name'];
                                    ?>
                                        <tr class="bg-light">
                                            <td colspan="7" class="font-weight-bold text-center" style="background-color: #e9ecef;">
                                                <i class="fas fa-layer-group mr-2"></i><?= esc($applicant['group_name']) ?>
                                            </td>
                                        </tr>
                                    <?php }
                                        $applicantCount++;
                                    ?>
                                        <tr <?= $isNewDate ? 'class="border-top border-dark"' : '' ?>>
                                            <td><?= $applicantCount ?></td>
                                            <td>
                                                <?php if ($isNewDate): ?>
                                                    <strong><?= date('l', strtotime($applicantDate)) ?></strong><br>
                                                    <?= date('d M Y', strtotime($applicantDate)) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= date('h:i A', strtotime($applicant['start_time'])) ?> -
                                                <?= date('h:i A', strtotime($applicant['end_time'])) ?>
                                            </td>
                                            <td>
                                                <strong><?= esc($applicant['position_no']) ?></strong><br>
                                                <?= esc($applicant['position_designation']) ?>
                                            </td>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <td><?= esc($applicant['contact_details']) ?></td>
                                            <td></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Grouped by Position Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-briefcase mr-2"></i>
                            Schedule by Position
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="positionAccordion">
                            <?php foreach ($scheduleData as $groupIndex => $group): ?>
                                <div class="card card-outline card-secondary mb-2">
                                    <div class="card-header" id="heading-<?= $group['id'] ?>">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapse-<?= $group['id'] ?>" aria-expanded="<?= $groupIndex === 0 ? 'true' : 'false' ?>" aria-controls="collapse-<?= $group['id'] ?>">
                                                <i class="fas fa-layer-group mr-2"></i>
                                                <?= esc($group['name']) ?>
                                                <span class="badge badge-primary float-right">
                                                    <?= count($group['positions']) ?> <?= count($group['positions']) > 1 ? 'Positions' : 'Position' ?>
                                                </span>
                                            </button>
                                        </h2>
                                    </div>

                                    <div id="collapse-<?= $group['id'] ?>" class="collapse <?= $groupIndex === 0 ? 'show' : '' ?>" aria-labelledby="heading-<?= $group['id'] ?>" data-parent="#positionAccordion">
                                        <div class="card-body p-0">
                                            <?php foreach ($group['positions'] as $position): ?>
                                                <div class="position-item border-bottom p-3">
                                                    <h5 class="text-primary">
                                                        <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                                                        <span class="badge badge-info float-right">
                                                            <?= count($position['applicants']) ?> <?= count($position['applicants']) > 1 ? 'Applicants' : 'Applicant' ?>
                                                        </span>
                                                    </h5>


                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-bordered">
                                                            <thead class="thead-light">
                                                                <tr>
                                                                    <th>Applicant</th>
                                                                    <th>Date</th>
                                                                    <th>Time</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($position['applicants'] as $applicant): ?>
                                                                    <tr>
                                                                        <td><?= esc($applicant['name']) ?></td>
                                                                        <td>
                                                                            <?= date('l', strtotime($applicant['date'])) ?>,
                                                                            <?= date('d M Y', strtotime($applicant['date'])) ?>
                                                                        </td>
                                                                        <td>
                                                                            <?= date('h:i A', strtotime($applicant['start_time'])) ?> -
                                                                            <?= date('h:i A', strtotime($applicant['end_time'])) ?>
                                                                        </td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#notificationModal_<?= $applicant['id'] ?>">
                                                                                <i class="fas fa-envelope"></i> Send Notification
                                                                            </button>

                                                                            <?php
                                                                            // Check if interview_notices exists and display ticks
                                                                            $noticeCount = 0;
                                                                            $notices = [];

                                                                            // Debug code removed for production

                                                                            if (!empty($applicant['interview_notices'])) {
                                                                                $notices = json_decode($applicant['interview_notices'], true);
                                                                                $noticeCount = is_array($notices) ? count($notices) : 0;

                                                                                if ($noticeCount > 0) {
                                                                                    echo '<span class="ml-2 badge badge-success" title="' . $noticeCount . ' notification(s) sent">';
                                                                                    for ($i = 0; $i < min($noticeCount, 3); $i++) {
                                                                                        echo '<i class="fas fa-check"></i>';
                                                                                    }
                                                                                    if ($noticeCount > 3) {
                                                                                        echo ' +' . ($noticeCount - 3);
                                                                                    }
                                                                                    echo '</span>';

                                                                                    // Add Notice Information button if notices exist
                                                                                    echo '<button type="button" class="btn btn-info btn-sm ml-2" data-toggle="modal" data-target="#noticeInfoModal_' . $applicant['id'] . '">';
                                                                                    echo '<i class="fas fa-info-circle"></i> Notice Info';
                                                                                    echo '</button>';
                                                                                }
                                                                            }
                                                                            ?>

                                                                            <!-- Notification Modal for Applicant <?= $applicant['id'] ?> -->
                                                                            <div class="modal fade" id="notificationModal_<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="notificationModalLabel_<?= $applicant['id'] ?>" aria-hidden="true">
                                                                                <div class="modal-dialog" role="document">
                                                                                    <div class="modal-content">
                                                                                        <div class="modal-header bg-primary">
                                                                                            <h5 class="modal-title" id="notificationModalLabel_<?= $applicant['id'] ?>">Send Interview Notification</h5>
                                                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                                <span aria-hidden="true">&times;</span>
                                                                                            </button>
                                                                                        </div>
                                                                                        <form action="<?= base_url('InterviewController/sendNotifications') ?>" method="post" class="notification-form">
                                                                                            <?= csrf_field() ?>
                                                                                        <div class="modal-body">
                                                                                            <div class="alert alert-info">
                                                                                                <i class="fas fa-info-circle mr-2"></i>
                                                                                                You are about to send an interview notification email.
                                                                                            </div>

                                                                                            <div class="applicant-details mb-3">
                                                                                                <h6>Applicant Information</h6>
                                                                                                <div class="row">
                                                                                                    <div class="col-md-4"><strong>Name:</strong></div>
                                                                                                    <div class="col-md-8"><?= esc($applicant['name']) ?></div>
                                                                                                </div>
                                                                                                <div class="row">
                                                                                                    <div class="col-md-4"><strong>Contact:</strong></div>
                                                                                                    <div class="col-md-8"><?= esc($applicant['contact_details'] ?? 'Not available') ?></div>
                                                                                                </div>
                                                                                                <div class="row">
                                                                                                    <div class="col-md-4"><strong>Position:</strong></div>
                                                                                                    <div class="col-md-8"><?= esc($position['designation']) ?> (<?= esc($position['position_no']) ?>)</div>
                                                                                                </div>
                                                                                                <div class="row">
                                                                                                    <div class="col-md-4"><strong>Interview Date:</strong></div>
                                                                                                    <div class="col-md-8"><?= date('l, j F Y', strtotime($applicant['date'])) ?></div>
                                                                                                </div>
                                                                                                <div class="row">
                                                                                                    <div class="col-md-4"><strong>Interview Time:</strong></div>
                                                                                                    <div class="col-md-8"><?= date('h:i A', strtotime($applicant['start_time'])) ?> - <?= date('h:i A', strtotime($applicant['end_time'])) ?></div>
                                                                                                </div>
                                                                                            </div>

                                                                                            <div class="form-group">
                                                                                                <label for="recipient_email_<?= $applicant['id'] ?>">Recipient Email <span class="text-danger">*</span></label>
                                                                                                <input type="email" class="form-control recipient-email" id="recipient_email_<?= $applicant['id'] ?>" name="recipient_email" placeholder="Enter email address" required>
                                                                                                <small class="form-text text-muted">The notification will be sent to this email address.</small>
                                                                                            </div>

                                                                                            <!-- Hidden inputs for applicant data -->
                                                                                            <input type="hidden" name="applicant_ids[]" value="<?= $applicant['id'] ?>">
                                                                                            <input type="hidden" name="interview_date" value="<?= $applicant['date'] ?>">
                                                                                            <input type="hidden" name="start_time" value="<?= $applicant['start_time'] ?>">
                                                                                            <input type="hidden" name="end_time" value="<?= $applicant['end_time'] ?>">

                                                                                            <div class="alert alert-warning">
                                                                                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                                                                                Make sure you have configured the interview communication settings in Settings > Interview Settings.
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="modal-footer">
                                                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                                            <button type="submit" class="btn btn-primary send-notification-submit">Send Notification</button>
                                                                                        </div>
                                                                                        </form>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <!-- Notice Information Modal for Applicant -->
                                                                            <?php
                                                                            // Make sure we have the notices variable properly set
                                                                            if (!isset($notices) || !is_array($notices)) {
                                                                                $notices = !empty($applicant['interview_notices']) ?
                                                                                    json_decode($applicant['interview_notices'], true) : [];
                                                                                $noticeCount = is_array($notices) ? count($notices) : 0;
                                                                            }

                                                                            if (!empty($notices) && $noticeCount > 0):
                                                                            ?>
                                                                            <div class="modal fade" id="noticeInfoModal_<?= $applicant['id'] ?>" tabindex="-1" role="dialog" aria-labelledby="noticeInfoModalLabel_<?= $applicant['id'] ?>" aria-hidden="true">
                                                                                <div class="modal-dialog" role="document">
                                                                                    <div class="modal-content">
                                                                                        <div class="modal-header bg-info">
                                                                                            <h5 class="modal-title" id="noticeInfoModalLabel_<?= $applicant['id'] ?>">Notification History</h5>
                                                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                                                <span aria-hidden="true">&times;</span>
                                                                                            </button>
                                                                                        </div>
                                                                                        <div class="modal-body">
                                                                                            <h6>Applicant: <?= $applicant['name'] ?></h6>
                                                                                            <p>Position: <?= $position['designation'] ?> (<?= $position['position_no'] ?>)</p>

                                                                                            <div class="table-responsive">
                                                                                                <table class="table table-bordered table-striped">
                                                                                                    <thead>
                                                                                                        <tr>
                                                                                                            <th>#</th>
                                                                                                            <th>Date & Time</th>
                                                                                                            <th>Sender</th>
                                                                                                            <th>Recipient</th>
                                                                                                            <th>Notification ID</th>
                                                                                                        </tr>
                                                                                                    </thead>
                                                                                                    <tbody>
                                                                                                        <?php foreach ($notices as $index => $notice): ?>
                                                                                                        <tr>
                                                                                                            <td><?= $index + 1 ?></td>
                                                                                                            <td><?= date('d/m/Y H:i', strtotime($notice['sent_datetime'])) ?></td>
                                                                                                            <td><?= $notice['sender_email'] ?></td>
                                                                                                            <td><?= $notice['receiver_email'] ?></td>
                                                                                                            <td><code><?= $notice['uid'] ?></code></td>
                                                                                                        </tr>
                                                                                                        <?php endforeach; ?>
                                                                                                    </tbody>
                                                                                                </table>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="modal-footer">
                                                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <?php endif; ?>
                                                                        </td>
                                                                    </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    console.log('Document ready - initializing modal handlers');

    // Handle notification form submission for all notification forms
    $(document).on('submit', '.notification-form', function() {
        const recipientEmail = $(this).find('.recipient-email').val();
        if (!recipientEmail) {
            alert('Please enter a recipient email address.');
            return false;
        }

        // Disable submit button to prevent multiple submissions
        $(this).find('.send-notification-submit').prop('disabled', true)
            .html('<i class="fas fa-spinner fa-spin mr-1"></i> Sending...');

        return true;
    });

    // Handle the main "Send Notifications" button (remove it or hide it)
    $('#sendNotificationsBtn').parent().hide();

    // Handle Print Table button
    $('#printTableBtn').on('click', function() {
        printInterviewScheduleTable();
    });

    // Handle Print Excel button
    $('#printExcelBtn').on('click', function() {
        printInterviewScheduleExcel();
    });
});

// Function to print only the Detailed Interview Schedule table
function printInterviewScheduleTable() {
    // Get organization name
    var orgName = "<?= isset($org['name']) ? addslashes($org['name']) : 'Organization' ?>";

    // Get the table content
    var tableContent = document.getElementById('schedule-table').outerHTML;

    // Create a new window for printing
    var printWindow = window.open('', '_blank');

    // Write the HTML content for printing
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Interview Schedule</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    color: #000;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .org-name {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .title {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 auto;
                }
                th, td {
                    border: 1px solid #000;
                    padding: 8px;
                    text-align: left;
                    font-size: 12px;
                }
                th {
                    background-color: #3c8dbc;
                    color: white;
                    font-weight: bold;
                    text-align: center;
                }
                .bg-light {
                    background-color: #e9ecef !important;
                    font-weight: bold;
                    text-align: center;
                }
                .border-top {
                    border-top: 2px solid #000 !important;
                }
                @media print {
                    body { margin: 0; }
                    .header { margin-bottom: 20px; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="org-name">${orgName}</div>
                <div class="title">Interview Schedule</div>
            </div>
            ${tableContent}
        </body>
        </html>
    `);

    // Close the document and print
    printWindow.document.close();
    printWindow.focus();

    // Wait a moment for content to load, then print
    setTimeout(function() {
        printWindow.print();
        printWindow.close();
    }, 250);
}

// Function to export only the Detailed Interview Schedule table to Excel
function printInterviewScheduleExcel() {
    // Get organization name
    var orgName = "<?= isset($org['name']) ? addslashes($org['name']) : 'Organization' ?>";

    // Create a workbook
    var wb = XLSX.utils.book_new();

    // Get the table element
    var table = document.getElementById('schedule-table');

    // Convert table to worksheet
    var ws = XLSX.utils.table_to_sheet(table);

    // Create header rows
    var headerRows = [
        [orgName],
        ['Interview Schedule'],
        [''], // Empty row for spacing
    ];

    // Insert header rows at the beginning
    XLSX.utils.sheet_add_aoa(ws, headerRows, { origin: 'A1' });

    // Adjust the range to include header rows
    var range = XLSX.utils.decode_range(ws['!ref']);
    range.s.r = 0; // Start from row 0
    ws['!ref'] = XLSX.utils.encode_range(range);

    // Style the header cells (organization name and title)
    if (ws['A1']) {
        ws['A1'].s = {
            font: { bold: true, sz: 16 },
            alignment: { horizontal: 'center' }
        };
    }
    if (ws['A2']) {
        ws['A2'].s = {
            font: { bold: true, sz: 14 },
            alignment: { horizontal: 'center' }
        };
    }

    // Merge cells for header rows to span across all columns
    var numCols = range.e.c + 1;
    ws['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: numCols - 1 } }, // Organization name
        { s: { r: 1, c: 0 }, e: { r: 1, c: numCols - 1 } }, // Title
        { s: { r: 2, c: 0 }, e: { r: 2, c: numCols - 1 } }  // Empty row
    ];

    // Set column widths
    ws['!cols'] = [
        { wch: 5 },   // # column
        { wch: 20 },  // Date column
        { wch: 15 },  // Time column
        { wch: 30 },  // Position column
        { wch: 25 },  // Applicant column
        { wch: 20 }   // Contacts column
    ];

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Interview Schedule");

    // Generate filename with current date
    var filename = "Interview_Schedule_" + new Date().toISOString().split('T')[0] + ".xlsx";

    // Save the file
    XLSX.writeFile(wb, filename);
}

$(document).ready(function() {
    // Excel export button handler
    $('.export-excel').on('click', function() {
        // Create a workbook
        var wb = XLSX.utils.book_new();

        // Get the table data
        var table = document.getElementById('schedule-table');
        var ws = XLSX.utils.table_to_sheet(table);

        // Set header
        var orgName = "<?= isset($org['name']) ? $org['name'] : 'Organization' ?>";
        var headerRows = [
            ['Interview Schedule for ' + orgName],
            ['Generated on ' + new Date().toLocaleDateString()]
        ];

        <?php if (!empty($interviewSettings['interview_venue'])): ?>
        headerRows.push(['Venue: <?= addslashes($interviewSettings['interview_venue']) ?>']);
        <?php endif; ?>

        <?php if (!empty($interviewSettings['reply_email'])): ?>
        headerRows.push(['Reply Email: <?= addslashes($interviewSettings['reply_email']) ?>']);
        <?php endif; ?>

        <?php if (!empty($interviewSettings['interview_contacts'])): ?>
        headerRows.push(['Contacts: <?= addslashes(str_replace("\n", " ", $interviewSettings['interview_contacts'])) ?>']);
        <?php endif; ?>

        XLSX.utils.sheet_add_aoa(ws, headerRows, { origin: 'A1' });

        // Add to workbook
        XLSX.utils.book_append_sheet(wb, ws, "Interview Schedule");

        // Save file
        XLSX.writeFile(wb, "Interview_Schedule_" + new Date().toISOString().split('T')[0] + ".xlsx");
    });

    // PDF export button handler
    $('.export-pdf').on('click', function() {
        // Initialize jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
        });

        // Add title
        var orgName = "<?= isset($org['name']) ? $org['name'] : 'Organization' ?>";
        doc.setFontSize(16);
        doc.text('Interview Schedule for ' + orgName, doc.internal.pageSize.getWidth() / 2, 20, { align: 'center' });
        doc.setFontSize(10);
        doc.text('Generated on ' + new Date().toLocaleDateString(), doc.internal.pageSize.getWidth() / 2, 28, { align: 'center' });

        // Add communication information if available
        var yPos = 35;
        <?php if (!empty($interviewSettings['interview_venue'])): ?>
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('Venue: ', 15, yPos);
        doc.setFont(undefined, 'normal');
        doc.text('<?= addslashes($interviewSettings['interview_venue']) ?>', 30, yPos);
        yPos += 5;
        <?php endif; ?>

        <?php if (!empty($interviewSettings['reply_email'])): ?>
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('Reply Email: ', 15, yPos);
        doc.setFont(undefined, 'normal');
        doc.text('<?= addslashes($interviewSettings['reply_email']) ?>', 40, yPos);
        yPos += 5;
        <?php endif; ?>

        // Prepare table data
        const rows = [];
        let currentGroup = '';
        let applicantNumber = 0;

        <?php foreach ($sortedApplicants as $applicant): ?>
            // Check if this is a new position group
            if ('<?= $applicant['group_name'] ?>' !== currentGroup) {
                currentGroup = '<?= $applicant['group_name'] ?>';
                rows.push([{ content: currentGroup, colSpan: 6, styles: { fillColor: [233, 236, 239], halign: 'center', fontStyle: 'bold' } }]);
            }

            // Increment applicant number
            applicantNumber++;

            // Add row data directly without declaring variables
            rows.push([
                applicantNumber.toString(),
                '<?= date('l, d M Y', strtotime($applicant['date'])) ?>',
                '<?= date('h:i A', strtotime($applicant['start_time'])) . ' - ' . date('h:i A', strtotime($applicant['end_time'])) ?>',
                '<?= $applicant['position_no'] ?> - <?= addslashes($applicant['position_designation']) ?>',
                '<?= addslashes($applicant['name']) ?>',
                '<?= addslashes($applicant['contact_details']) ?>'
            ]);
        <?php endforeach; ?>

        // Generate the table with adjusted column widths
        doc.autoTable({
            startY: yPos + 5,
            head: [['#', 'Date', 'Time', 'Position', 'Applicant', 'Contacts']],
            body: rows,
            headStyles: { fillColor: [60, 141, 188], textColor: 255 },
            columnStyles: {
                0: { minCellWidth: 10 },
                1: { minCellWidth: 40 },
                2: { minCellWidth: 30 },
                3: { minCellWidth: 55 },
                4: { minCellWidth: 35 },
                5: { minCellWidth: 30 }
            },
            margin: { left: 5, right: 5, top: 35, bottom: 5 },
            styles: { fontSize: 9 },
            tableWidth: 'auto',
            useCss: true,
            theme: 'grid'
        });

        // Save PDF
        doc.save("Interview_Schedule_" + new Date().toISOString().split('T')[0] + ".pdf");
    });
});
</script>
<?= $this->endSection() ?>

