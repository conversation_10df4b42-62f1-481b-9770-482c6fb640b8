<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Interview Settings
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('settings') ?>">Settings</a></li>
                        <li class="breadcrumb-item active">Interview Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-cog mr-2"></i>
                                Interview Schedule Configuration
                            </h3>
                            <?php if (isset($current_exercise)): ?>
                            <div class="card-tools">
                                <span class="badge badge-info">
                                    <i class="fas fa-dumbbell mr-1"></i>
                                    Exercise: <?= esc($current_exercise['exercise_title']) ?>
                                </span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?= form_open('settings/save_interview_settings') ?>
                        <div class="card-body">
                            <?php if (isset($current_exercise)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>Exercise-Specific Settings:</strong> These interview settings apply only to the current exercise
                                "<strong><?= esc($current_exercise['exercise_title']) ?></strong>". Each exercise can have its own independent interview configuration.
                            </div>
                            <?php endif; ?>
                            <div class="row">
                                <!-- Basic Interview Settings -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="minutes_per_interview">Minutes Per Interview</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="minutes_per_interview" name="minutes_per_interview"
                                                value="<?= esc($settings['minutes_per_interview']) ?>" min="1" max="120" required>
                                            <div class="input-group-append">
                                                <span class="input-group-text">minutes</span>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">How long each interview will last</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="transition_time">Transition Time Between Interviews</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="transition_time" name="transition_time"
                                                value="<?= esc($settings['transition_time']) ?>" min="0" max="60" required>
                                            <div class="input-group-append">
                                                <span class="input-group-text">minutes</span>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">Buffer time between consecutive interviews</small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Daily Interview Time Range</label>
                                        <div class="row">
                                            <div class="col-6">
                                                <label for="start_time">Start Time</label>
                                                <input type="time" class="form-control" id="start_time" name="start_time"
                                                    value="<?= esc($settings['start_time']) ?>" required>
                                            </div>
                                            <div class="col-6">
                                                <label for="end_time">End Time</label>
                                                <input type="time" class="form-control" id="end_time" name="end_time"
                                                    value="<?= esc($settings['end_time']) ?>" required>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">The time range for scheduling interviews each day</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="start_date">Interview Start Date</label>
                                        <input type="date" class="form-control" id="start_date" name="start_date"
                                            value="<?= esc($settings['start_date']) ?>" required>
                                        <small class="form-text text-muted">The date when interviews will begin</small>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <!-- Break Times -->
                            <div class="row">
                                <div class="col-12">
                                    <h4><i class="fas fa-coffee mr-2"></i>Break Times</h4>
                                    <p class="text-muted">Define periods during the day when no interviews should be scheduled (e.g., lunch breaks)</p>

                                    <div id="break-times-container">
                                        <?php if (!empty($settings['break_times'])): ?>
                                            <?php foreach ($settings['break_times'] as $index => $break): ?>
                                                <div class="row break-time-row mb-2">
                                                    <div class="col-md-5">
                                                        <div class="input-group">
                                                            <div class="input-group-prepend">
                                                                <span class="input-group-text">Start</span>
                                                            </div>
                                                            <input type="time" class="form-control" name="break_start_time[]" value="<?= esc($break['start']) ?>">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-5">
                                                        <div class="input-group">
                                                            <div class="input-group-prepend">
                                                                <span class="input-group-text">End</span>
                                                            </div>
                                                            <input type="time" class="form-control" name="break_end_time[]" value="<?= esc($break['end']) ?>">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-danger remove-break-time">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="row break-time-row mb-2">
                                                <div class="col-md-5">
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">Start</span>
                                                        </div>
                                                        <input type="time" class="form-control" name="break_start_time[]" value="12:00">
                                                    </div>
                                                </div>
                                                <div class="col-md-5">
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">End</span>
                                                        </div>
                                                        <input type="time" class="form-control" name="break_end_time[]" value="13:00">
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-danger remove-break-time">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <button type="button" id="add-break-time" class="btn btn-success mt-2">
                                        <i class="fas fa-plus mr-1"></i> Add Break Time
                                    </button>
                                </div>
                            </div>

                            <hr>

                            <!-- Break Days -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h4><i class="fas fa-calendar-times mr-2"></i>Break Days</h4>
                                    <p class="text-muted">Select days when no interviews should be scheduled (e.g., weekends, holidays)</p>

                                    <div id="break-days-container">
                                        <?php if (!empty($settings['break_days'])): ?>
                                            <?php foreach ($settings['break_days'] as $index => $day): ?>
                                                <div class="row break-day-row mb-2">
                                                    <div class="col-md-10">
                                                        <div class="input-group">
                                                            <div class="input-group-prepend">
                                                                <span class="input-group-text">Date</span>
                                                            </div>
                                                            <input type="date" class="form-control" name="break_days[]" value="<?= esc($day) ?>">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button" class="btn btn-danger remove-break-day">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="row break-day-row mb-2">
                                                <div class="col-md-10">
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text">Date</span>
                                                        </div>
                                                        <input type="date" class="form-control" name="break_days[]">
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-danger remove-break-day">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <button type="button" id="add-break-day" class="btn btn-success mt-2">
                                        <i class="fas fa-plus mr-1"></i> Add Break Day
                                    </button>
                                </div>
                            </div>

                            <hr>

                            <!-- Interview Communication Settings -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h4><i class="fas fa-envelope mr-2"></i>Interview Communication Settings</h4>
                                    <p class="text-muted">Configure details for interview communications with candidates</p>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="interview_venue">Interview Venue</label>
                                                <input type="text" class="form-control" id="interview_venue" name="interview_venue"
                                                    value="<?= esc($settings['interview_venue'] ?? '') ?>" placeholder="Enter interview location">
                                                <small class="form-text text-muted">Physical location where interviews will be conducted</small>
                                            </div>

                                            <div class="form-group">
                                                <label for="reply_email">Reply to Email Address</label>
                                                <input type="email" class="form-control" id="reply_email" name="reply_email"
                                                    value="<?= esc($settings['reply_email'] ?? '') ?>" placeholder="Enter email address">
                                                <small class="form-text text-muted">Email address that interviewees will reply to</small>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="interview_contacts">Interview Contacts</label>
                                                <textarea class="form-control" id="interview_contacts" name="interview_contacts"
                                                    rows="3" placeholder="Enter contact information"><?= esc($settings['interview_contacts'] ?? '') ?></textarea>
                                                <small class="form-text text-muted">Contact information for interview-related inquiries</small>
                                            </div>

                                            <div class="form-group">
                                                <label for="additional_messages">Additional Messages</label>
                                                <textarea class="form-control" id="additional_messages" name="additional_messages"
                                                    rows="3" placeholder="Enter additional information"><?= esc($settings['additional_messages'] ?? '') ?></textarea>
                                                <small class="form-text text-muted">Additional information to include in interview communications</small>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="sign_off_signature">Sign-off Signature</label>
                                                <textarea class="form-control" id="sign_off_signature" name="sign_off_signature"
                                                    rows="3" placeholder="Enter signature for communications"><?= esc($settings['sign_off_signature'] ?? '') ?></textarea>
                                                <small class="form-text text-muted">Signature to be included at the end of interview communications</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer bg-white">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save mr-1"></i> Save Interview Settings
                            </button>
                            <a href="<?= base_url('settings') ?>" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left mr-1"></i> Back to Settings
                            </a>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add new break time row
    $('#add-break-time').click(function() {
        var newRow = `
            <div class="row break-time-row mb-2">
                <div class="col-md-5">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Start</span>
                        </div>
                        <input type="time" class="form-control" name="break_start_time[]">
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">End</span>
                        </div>
                        <input type="time" class="form-control" name="break_end_time[]">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger remove-break-time">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        $('#break-times-container').append(newRow);
    });

    // Remove break time row
    $(document).on('click', '.remove-break-time', function() {
        if ($('.break-time-row').length > 1) {
            $(this).closest('.break-time-row').remove();
        } else {
            alert('You must have at least one break time row');
        }
    });

    // Add new break day row
    $('#add-break-day').click(function() {
        var newRow = `
            <div class="row break-day-row mb-2">
                <div class="col-md-10">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Date</span>
                        </div>
                        <input type="date" class="form-control" name="break_days[]">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger remove-break-day">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        $('#break-days-container').append(newRow);
    });

    // Remove break day row
    $(document).on('click', '.remove-break-day', function() {
        if ($('.break-day-row').length > 1) {
            $(this).closest('.break-day-row').remove();
        } else {
            alert('You must have at least one break day row');
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>