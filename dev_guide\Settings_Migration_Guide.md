# Settings Migration Guide: DakoiiOrgModel → ExerciseSettingsModel

## Overview

This guide provides step-by-step instructions for migrating settings functionality from organization-level (`DakoiiOrgModel`) to exercise-specific settings (`ExerciseSettingsModel`). This migration enables each recruitment exercise to have its own independent settings configuration.

## Table of Contents

1. [Database Setup](#database-setup)
2. [Understanding the Migration](#understanding-the-migration)
3. [Code Migration Steps](#code-migration-steps)
4. [Helper Functions](#helper-functions)
5. [Data Migration](#data-migration)
6. [Testing](#testing)
7. [Cleanup](#cleanup)

## Database Setup

### 1. Create the exercise_settings Table

```sql
CREATE TABLE `exercise_settings` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `exercise_id` int(11) unsigned NOT NULL,
  `org_id` int(11) unsigned NOT NULL,
  `advertisement_no` varchar(255) DEFAULT NULL COMMENT 'Advertisement number',
  `advertisement_date` date DEFAULT NULL COMMENT 'Advertisement date',
  `mode_of_advert` varchar(255) DEFAULT NULL COMMENT 'Mode of advertisement',
  `sort_out_of` int(11) DEFAULT NULL COMMENT 'Maximum score for sorting/ranking',
  `introduction` text DEFAULT NULL COMMENT 'Introduction text',
  `composition` text DEFAULT NULL COMMENT 'Composition details',
  `criteria` text DEFAULT NULL COMMENT 'Selection criteria',
  `culling` text DEFAULT NULL COMMENT 'Culling process',
  `interview_settings` json DEFAULT NULL COMMENT 'Interview configuration JSON',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `exercise_org_unique` (`exercise_id`, `org_id`),
  KEY `idx_exercise_id` (`exercise_id`),
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Understanding the Migration

### Field Mapping

| DakoiiOrgModel Field | ExerciseSettingsModel Field | Type | Description |
|---------------------|----------------------------|------|-------------|
| `advertisement_no` | `advertisement_no` | varchar(255) | Advertisement number |
| `advertisement_date` | `advertisement_date` | date | Advertisement date |
| `mode_of_advert` | `mode_of_advert` | varchar(255) | Mode of advertisement |
| `sort_out_of` | `sort_out_of` | int(11) | Maximum score for sorting |
| `introduction` | `introduction` | text | Introduction text |
| `composition` | `composition` | text | Composition details |
| `criteria` | `criteria` | text | Selection criteria |
| `culling` | `culling` | text | Culling process |
| `interview_settings` | `interview_settings` | json | Interview configuration |

### Key Differences

- **Organization-level** → **Exercise-level**: Settings are now per exercise, not per organization
- **Single record** → **Multiple records**: Each exercise has its own settings record
- **Direct access** → **Helper functions**: Use helper functions for consistent access

## Code Migration Steps

### Step 1: Update Controllers

#### Before (DakoiiOrgModel):
```php
// Old way - accessing organization settings
$orgModel = new DakoiiOrgModel();
$org = $orgModel->find(session('org_id'));
$sortOutOf = $org['sort_out_of'];
$introduction = $org['introduction'];
$interviewSettings = json_decode($org['interview_settings'], true);
```

#### After (ExerciseSettingsModel):
```php
// New way - accessing exercise settings
helper('exercise_settings');
$sortOutOf = get_sort_out_of();
$introduction = get_introduction();
$interviewSettings = get_interview_settings();
```

### Step 2: Update Views

#### Before:
```php
// In views - old way
<?php 
$orgModel = new \App\Models\DakoiiOrgModel();
$org = $orgModel->find(session('org_id'));
?>
<p>Advertisement No: <?= $org['advertisement_no'] ?></p>
<p>Sort Out Of: <?= $org['sort_out_of'] ?></p>
```

#### After:
```php
// In views - new way
<?php helper('exercise_settings'); ?>
<p>Advertisement No: <?= get_advertisement_no() ?></p>
<p>Sort Out Of: <?= get_sort_out_of() ?></p>
```

### Step 3: Update Form Processing

#### Before:
```php
// Old way - updating organization settings
$orgModel = new DakoiiOrgModel();
$data = [
    'advertisement_no' => $this->request->getPost('advertisement_no'),
    'sort_out_of' => $this->request->getPost('sort_out_of'),
    'introduction' => $this->request->getPost('introduction')
];
$orgModel->update(session('org_id'), $data);
```

#### After:
```php
// New way - updating exercise settings
helper('exercise_settings');
set_exercise_setting('advertisement_no', $this->request->getPost('advertisement_no'));
set_exercise_setting('sort_out_of', $this->request->getPost('sort_out_of'));
set_exercise_setting('introduction', $this->request->getPost('introduction'));

// Or update multiple fields at once
$settingsModel = new ExerciseSettingsModel();
$data = [
    'advertisement_no' => $this->request->getPost('advertisement_no'),
    'sort_out_of' => $this->request->getPost('sort_out_of'),
    'introduction' => $this->request->getPost('introduction')
];
$settingsModel->saveExerciseSettings(get_current_exercise_id(), $data);
```

## Helper Functions

### Available Helper Functions

```php
// Load the helper
helper('exercise_settings');

// Get individual settings
$adNo = get_advertisement_no();
$adDate = get_advertisement_date();
$modeAdvert = get_mode_of_advert();
$sortOutOf = get_sort_out_of();
$intro = get_introduction();
$composition = get_composition();
$criteria = get_criteria();
$culling = get_culling();
$interviewSettings = get_interview_settings();

// Set individual settings
set_exercise_setting('advertisement_no', 'ADV-2024-001');
set_exercise_setting('sort_out_of', 100);
set_interview_settings([
    'minutes_per_interviewee' => 30,
    'transition_time' => 5,
    'start_time' => '09:00',
    'end_time' => '17:00'
]);

// Get all settings
$allSettings = get_all_exercise_settings();

// Initialize settings for new exercise
initialize_exercise_settings($exerciseId);

// Migrate from organization settings
migrate_org_settings_to_exercise();
```

### Direct Model Usage

```php
$settingsModel = new ExerciseSettingsModel();

// Get all settings for an exercise
$settings = $settingsModel->getExerciseSettings($exerciseId);

// Get specific field
$sortOutOf = $settingsModel->getSettingField($exerciseId, 'sort_out_of');

// Set specific field
$settingsModel->setSettingField($exerciseId, 'sort_out_of', 100);

// Save multiple fields
$data = [
    'advertisement_no' => 'ADV-2024-001',
    'sort_out_of' => 100,
    'introduction' => 'Welcome to our recruitment process'
];
$settingsModel->saveExerciseSettings($exerciseId, $data);

// Copy settings between exercises
$settingsModel->copySettings($fromExerciseId, $toExerciseId);

// Migrate from organization data
$orgModel = new DakoiiOrgModel();
$orgData = $orgModel->find($orgId);
$settingsModel->migrateFromOrgSettings($exerciseId, $orgData);
```

## Data Migration

### Automatic Migration

Use the built-in migration function to copy existing organization settings to exercise settings:

```php
// In your controller or migration script
helper('exercise_settings');

// Migrate current organization settings to current exercise
$migrated = migrate_org_settings_to_exercise();
echo "Migrated {$migrated} settings to current exercise";

// Or migrate specific exercise
$settingsModel = new ExerciseSettingsModel();
$orgModel = new DakoiiOrgModel();
$orgData = $orgModel->find($orgId);
$success = $settingsModel->migrateFromOrgSettings($exerciseId, $orgData);
```

### Manual Migration Script

```php
// Create a migration script
$settingsModel = new ExerciseSettingsModel();
$orgModel = new DakoiiOrgModel();
$exerciseModel = new ExerciseModel();

// Get all organizations
$organizations = $orgModel->findAll();

foreach ($organizations as $org) {
    // Get all exercises for this organization
    $exercises = $exerciseModel->where('org_id', $org['id'])->findAll();
    
    foreach ($exercises as $exercise) {
        // Migrate settings for each exercise
        $success = $settingsModel->migrateFromOrgSettings($exercise['id'], $org);
        echo "Migrated settings for Exercise {$exercise['id']}: " . ($success ? 'Success' : 'Failed') . "\n";
    }
}
```

## Testing

### Test Migration

1. **Verify Data Migration**:
```php
// Check if settings were migrated correctly
$settingsModel = new ExerciseSettingsModel();
$settings = $settingsModel->getExerciseSettings($exerciseId);
var_dump($settings);
```

2. **Test Helper Functions**:
```php
helper('exercise_settings');
echo "Advertisement No: " . get_advertisement_no() . "\n";
echo "Sort Out Of: " . get_sort_out_of() . "\n";
echo "Introduction: " . get_introduction() . "\n";
```

3. **Test Form Updates**:
```php
// Test setting values
set_exercise_setting('advertisement_no', 'TEST-001');
$result = get_advertisement_no();
echo "Test result: " . $result; // Should output: TEST-001
```

### Validation Checklist

- [ ] Database table created successfully
- [ ] Helper functions work correctly
- [ ] Settings can be retrieved and updated
- [ ] Forms save data to exercise_settings table
- [ ] Views display exercise-specific settings
- [ ] Migration from organization settings works
- [ ] Multiple exercises have independent settings

## Cleanup

### After Successful Migration

1. **Update all controllers** to use exercise settings
2. **Update all views** to use helper functions
3. **Test thoroughly** in development environment
4. **Deploy to production** with data migration
5. **Monitor** for any issues

### Optional: Remove Old Fields

After confirming everything works, you can remove the settings fields from `DakoiiOrgModel`:

```php
// Remove these fields from DakoiiOrgModel allowedFields:
// 'advertisement_no',
// 'advertisement_date', 
// 'mode_of_advert',
// 'sort_out_of',
// 'introduction',
// 'composition',
// 'criteria',
// 'culling',
// 'interview_settings'
```

## Common Issues & Solutions

### Issue 1: Settings Not Found
**Problem**: Helper functions return null
**Solution**: Ensure exercise context is set and settings are initialized

```php
// Check exercise context
if (!has_exercise_context()) {
    redirect()->to('/exercise');
}

// Initialize settings if needed
initialize_exercise_settings(get_current_exercise_id());
```

### Issue 2: JSON Handling
**Problem**: Interview settings not parsing correctly
**Solution**: Use proper JSON handling

```php
// Correct way to handle interview settings
$settings = get_interview_settings();
if (is_array($settings)) {
    $duration = $settings['minutes_per_interviewee'] ?? 30;
}
```

### Issue 3: Migration Errors
**Problem**: Migration fails silently
**Solution**: Check for existing records and handle errors

```php
$settingsModel = new ExerciseSettingsModel();
try {
    $result = $settingsModel->migrateFromOrgSettings($exerciseId, $orgData);
    if (!$result) {
        log_message('error', 'Migration failed for exercise: ' . $exerciseId);
    }
} catch (Exception $e) {
    log_message('error', 'Migration error: ' . $e->getMessage());
}
```

## Conclusion

This migration transforms the settings system from organization-level to exercise-specific, enabling:

- **Independent exercise configurations**
- **Better data isolation**
- **Flexible recruitment management**
- **Scalable multi-exercise support**

Follow this guide step-by-step to ensure a smooth migration from DakoiiOrgModel to ExerciseSettingsModel.
