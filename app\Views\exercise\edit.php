<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-edit mr-2"></i>
                        <?= $title ?>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('exercise') ?>">Exercises</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('exercise/show/' . $exercise['id']) ?>"><?= esc($exercise['exercise_title']) ?></a></li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-edit mr-2"></i>
                                Edit Exercise
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-<?= $exercise['status'] === 'active' ? 'success' : ($exercise['status'] === 'completed' ? 'info' : ($exercise['status'] === 'archived' ? 'secondary' : 'warning')) ?>">
                                    <?= ucfirst($exercise['status']) ?>
                                </span>
                            </div>
                        </div>
                        
                        <?= form_open('exercise/update/' . $exercise['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="card-body">
                            <!-- Exercise Title -->
                            <div class="form-group">
                                <label for="exercise_title" class="required">Exercise Title</label>
                                <input type="text" 
                                       class="form-control <?= $validation->hasError('exercise_title') ? 'is-invalid' : '' ?>" 
                                       id="exercise_title" 
                                       name="exercise_title" 
                                       value="<?= old('exercise_title', $exercise['exercise_title']) ?>" 
                                       placeholder="Enter exercise title"
                                       required>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('exercise_title') ?>
                                </div>
                                <small class="form-text text-muted">
                                    Choose a descriptive title that identifies this recruitment round
                                </small>
                            </div>

                            <!-- Description -->
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control <?= $validation->hasError('description') ? 'is-invalid' : '' ?>" 
                                          id="description" 
                                          name="description" 
                                          rows="4" 
                                          placeholder="Enter exercise description (optional)"><?= old('description', $exercise['description']) ?></textarea>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('description') ?>
                                </div>
                                <small class="form-text text-muted">
                                    Provide additional details about this exercise (optional)
                                </small>
                            </div>

                            <!-- Exercise Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Created</span>
                                            <span class="info-box-number"><?= date('M d, Y', strtotime($exercise['created_at'])) ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-warning">
                                            <i class="fas fa-clock"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">Last Updated</span>
                                            <span class="info-box-number"><?= date('M d, Y', strtotime($exercise['updated_at'])) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Status Information -->
                            <?php if ($exercise['status'] !== 'draft'): ?>
                                <div class="alert alert-warning">
                                    <h5><i class="icon fas fa-exclamation-triangle"></i> Note</h5>
                                    This exercise is currently <strong><?= ucfirst($exercise['status']) ?></strong>. 
                                    You can still edit the title and description, but be careful not to confuse users 
                                    who may already be working with this exercise.
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="<?= base_url('exercise/show/' . $exercise['id']) ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left mr-1"></i>
                                        Back to Exercise
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save mr-1"></i>
                                        Update Exercise
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
.required:after {
    content: " *";
    color: red;
}
</style>

<?= $this->endSection() ?>
