<?php

/**
 * Exercise Helper Functions
 * 
 * These functions provide exercise context management throughout the application
 */

if (!function_exists('get_current_exercise')) {
    /**
     * Get the current exercise data from session
     *
     * @return array|null
     */
    function get_current_exercise()
    {
        return session('current_exercise');
    }
}

if (!function_exists('get_current_exercise_id')) {
    /**
     * Get the current exercise ID from session
     *
     * @return int|null
     */
    function get_current_exercise_id()
    {
        return session('current_exercise_id');
    }
}

if (!function_exists('has_exercise_context')) {
    /**
     * Check if exercise context is set
     *
     * @return bool
     */
    function has_exercise_context()
    {
        return !empty(session('current_exercise_id'));
    }
}

if (!function_exists('require_exercise_context')) {
    /**
     * Require exercise context or throw exception
     *
     * @throws \CodeIgniter\Exceptions\PageNotFoundException
     */
    function require_exercise_context()
    {
        if (!has_exercise_context()) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Exercise context required');
        }
    }
}

if (!function_exists('set_exercise_context')) {
    /**
     * Set exercise context in session
     *
     * @param int $exerciseId
     * @param array $exerciseData
     * @return void
     */
    function set_exercise_context($exerciseId, $exerciseData = null)
    {
        session()->set('current_exercise_id', $exerciseId);
        
        if ($exerciseData) {
            session()->set('current_exercise', $exerciseData);
        }
    }
}

if (!function_exists('clear_exercise_context')) {
    /**
     * Clear exercise context from session
     *
     * @return void
     */
    function clear_exercise_context()
    {
        session()->remove('current_exercise_id');
        session()->remove('current_exercise');
    }
}

if (!function_exists('get_exercise_status_badge')) {
    /**
     * Get Bootstrap badge class for exercise status
     *
     * @param string $status
     * @return string
     */
    function get_exercise_status_badge($status)
    {
        switch ($status) {
            case 'active':
                return 'badge-success';
            case 'completed':
                return 'badge-info';
            case 'archived':
                return 'badge-secondary';
            case 'draft':
            default:
                return 'badge-warning';
        }
    }
}

if (!function_exists('get_exercise_status_color')) {
    /**
     * Get color class for exercise status
     *
     * @param string $status
     * @return string
     */
    function get_exercise_status_color($status)
    {
        switch ($status) {
            case 'active':
                return 'success';
            case 'completed':
                return 'info';
            case 'archived':
                return 'secondary';
            case 'draft':
            default:
                return 'warning';
        }
    }
}

if (!function_exists('validate_exercise_access')) {
    /**
     * Validate that user has access to the exercise
     *
     * @param int $exerciseId
     * @param int $orgId
     * @return bool
     */
    function validate_exercise_access($exerciseId, $orgId)
    {
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercise = $exerciseModel->find($exerciseId);
        
        return $exercise && $exercise['org_id'] == $orgId;
    }
}

if (!function_exists('get_exercise_breadcrumb')) {
    /**
     * Get breadcrumb array for exercise context
     *
     * @return array
     */
    function get_exercise_breadcrumb()
    {
        $breadcrumb = [
            ['title' => 'Home', 'url' => base_url('dashboard')],
            ['title' => 'Exercises', 'url' => base_url('exercise')]
        ];
        
        if (has_exercise_context()) {
            $exercise = get_current_exercise();
            if ($exercise) {
                $breadcrumb[] = [
                    'title' => $exercise['exercise_title'],
                    'url' => base_url('exercise/show/' . $exercise['id'])
                ];
            }
        }
        
        return $breadcrumb;
    }
}

if (!function_exists('format_exercise_date')) {
    /**
     * Format exercise date for display
     *
     * @param string $date
     * @param string $format
     * @return string
     */
    function format_exercise_date($date, $format = 'M d, Y')
    {
        return date($format, strtotime($date));
    }
}

if (!function_exists('get_exercise_actions')) {
    /**
     * Get available actions for exercise based on status
     *
     * @param array $exercise
     * @return array
     */
    function get_exercise_actions($exercise)
    {
        $actions = [
            'select' => true,
            'view' => true,
            'edit' => true
        ];
        
        switch ($exercise['status']) {
            case 'draft':
                $actions['activate'] = true;
                $actions['delete'] = true; // Only if no data
                break;
            case 'active':
                $actions['complete'] = true;
                break;
            case 'completed':
                $actions['archive'] = true;
                break;
            case 'archived':
                // Only view and select actions available
                break;
        }
        
        return $actions;
    }
}

if (!function_exists('is_exercise_editable')) {
    /**
     * Check if exercise can be edited based on status
     *
     * @param string $status
     * @return bool
     */
    function is_exercise_editable($status)
    {
        return in_array($status, ['draft', 'active', 'completed']);
    }
}

if (!function_exists('get_exercise_context_alert')) {
    /**
     * Get exercise context alert HTML
     *
     * @return string
     */
    function get_exercise_context_alert()
    {
        if (!has_exercise_context()) {
            return '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        No exercise selected. Please <a href="' . base_url('exercise') . '">select an exercise</a> to continue.
                    </div>';
        }
        
        $exercise = get_current_exercise();
        if (!$exercise) {
            return '';
        }
        
        $statusColor = get_exercise_status_color($exercise['status']);
        
        return '<div class="alert alert-info alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>Current Exercise:</strong> ' . esc($exercise['exercise_title']) . '
                    <span class="badge badge-' . $statusColor . ' ml-2">' . ucfirst($exercise['status']) . '</span>
                    <a href="' . base_url('exercise') . '" class="btn btn-sm btn-outline-info ml-2">Change Exercise</a>
                </div>';
    }
}
