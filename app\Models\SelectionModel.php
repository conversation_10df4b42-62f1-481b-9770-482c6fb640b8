<?php

namespace App\Models;

use CodeIgniter\Model;

class SelectionModel extends Model
{
    protected $table      = 'selection';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'exercise_id',
        'box',
        'value',
        'item'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = false;

    // Optional: Soft deletes can be enabled if needed
    protected $useSoftDeletes = false;

    /**
     * Get selection items by box
     *
     * @param string $box
     * @return array
     */
    public function getSelectionByBox($box)
    {
        return $this->where('box', $box)
                    ->orderBy('value', 'ASC')
                    ->findAll();
    }

    /**
     * Get selection items by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getSelectionByExercise($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('box', 'ASC')
                    ->orderBy('value', 'ASC')
                    ->findAll();
    }

    /**
     * Get selection items by exercise ID and box
     *
     * @param int $exerciseId
     * @param string $box
     * @return array
     */
    public function getSelectionByExerciseAndBox($exerciseId, $box)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->where('box', $box)
                    ->orderBy('value', 'ASC')
                    ->findAll();
    }
}
