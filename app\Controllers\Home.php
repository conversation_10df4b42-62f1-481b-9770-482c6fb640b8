<?php

namespace App\Controllers;

use App\Models\ApplicantModel;
use App\Models\PositionModel;
use App\Models\UsersModel;
use App\Models\DakoiiOrgModel;

class Home extends BaseController
{
    protected $session;
    protected $usersModel;
    protected $positionsModel;
    protected $applicantsModel;
    protected $orgModel;


    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->usersModel = new UsersModel();
        $this->positionsModel = new PositionModel();
        $this->applicantsModel = new ApplicantModel();
        $this->orgModel = new DakoiiOrgModel();
    }

    public function index()
    {
        // Get all active organizations with position counts
        $organizations = $this->orgModel->where('is_active', 1)
                                      ->orderBy('name', 'ASC')
                                      ->findAll();

        // Get position counts for each organization
        foreach ($organizations as &$org) {
            $org['position_count'] = $this->positionsModel->where('org_id', $org['id'])->countAllResults();
            $org['rated_count'] = $this->applicantsModel
                ->where('org_id', $org['id'])
                ->where('rate_qualification >', 0)
                ->countAllResults();
        }

        $data = [
            'title' => "Home",
            'menu' => "home",
            'validation' => \Config\Services::validation(),
            'organizations' => $organizations
        ];

        return view('home/home', $data);
    }

    public function login()
    {
        $validation = \Config\Services::validation();
        $validation->setRules([
            'username' => 'required',
            'password' => 'required'
        ]);

        if ($validation->withRequest($this->request)->run()) {
            $username = $this->request->getPost('username');
            $password = $this->request->getPost('password');

            $user = $this->usersModel->where('username', $username)->first();



            if ($user && password_verify($password, $user['password'])) {
                //get org_name from org_id
                $org = $this->orgModel->find($user['org_id']);
                $this->session->set([
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'role' => $user['role'],
                    'org_id' => $user['org_id'],
                    'org_name' => $org['name'],
                    'is_loggedin' => true
                ]);

                return redirect()->to('dashboard');
            } else {
                $this->session->setFlashdata('error', 'Invalid username or password');
                return redirect()->back()->withInput();
            }
        } else {
            return redirect()->back()->withInput()->with('validation', $validation);
        }
    }


    public function dashboard()
    {
        // Get total counts
        $positions_count = $this->positionsModel->where('org_id', session('org_id'))->countAllResults();
        $applicants_count = $this->applicantsModel->where('org_id', session('org_id'))->countAllResults();
        $rated_count = $this->applicantsModel->where('org_id', session('org_id'))->where('rate_qualification >', 0)->countAllResults();
        
        // Get recent applicants
        $recent_applicants = $this->applicantsModel
            ->select('applicants.*, positions.position_no, positions.designation')
            ->join('positions', 'positions.id = applicants.position_id', 'left')
            ->where('applicants.org_id', session('org_id'))
            ->orderBy('applicants.created_at', 'DESC')
            ->limit(5)
            ->find();

        // Get positions with most applicants
        $top_positions = $this->positionsModel
            ->select('positions.*, COUNT(applicants.id) as applicant_count')
            ->join('applicants', 'applicants.position_id = positions.id', 'left')
            ->where('positions.org_id', session('org_id'))
            ->groupBy('positions.id')
            ->orderBy('applicant_count', 'DESC')
            ->limit(5)
            ->find();

        $data = [
            'title' => "Dashboard",
            'menu' => "dashboard",
            'user' => $this->usersModel->where('id', session('user_id'))->first(),
            'positions_count' => $positions_count,
            'applicants_count' => $applicants_count,
            'ratings_count' => $rated_count,
            'recent_applicants' => $recent_applicants,
            'top_positions' => $top_positions,
            'completion_rate' => $applicants_count > 0 ? round(($rated_count / $applicants_count) * 100) : 0
        ];

        return view('dashboard/index', $data);
    }

    public function logout()
    {
        $this->session->destroy();
        return redirect()->to(base_url());
    }
}
