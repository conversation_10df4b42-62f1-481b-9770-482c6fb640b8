<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\ApplicantModel;
use App\Models\PositionModel;
use App\Models\InterviewerModel;
use App\Models\InterviewQuestionModel;

class Exercise extends BaseController
{
    protected $exerciseModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = session();
        $this->exerciseModel = new ExerciseModel();
    }

    /**
     * Display list of exercises for the organization
     */
    public function index()
    {
        $orgId = $this->session->get('org_id');
        
        if (!$orgId) {
            return redirect()->to('/')->with('error', 'Please login first');
        }

        $exercises = $this->exerciseModel->getExercisesByOrg($orgId);

        // Add statistics to each exercise
        foreach ($exercises as &$exercise) {
            $exercise['stats'] = $this->getExerciseStats($exercise['id']);
        }

        $data = [
            'exercises' => $exercises,
            'title' => 'Exercise Management',
            'menu' => 'exercises'
        ];

        return view('exercise/index', $data);
    }

    /**
     * Show create exercise form
     */
    public function create()
    {
        $data = [
            'title' => 'Create New Exercise',
            'menu' => 'exercises',
            'validation' => \Config\Services::validation()
        ];

        return view('exercise/create', $data);
    }

    /**
     * Store new exercise
     */
    public function store()
    {
        $rules = [
            'exercise_title' => 'required|max_length[255]',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return view('exercise/create', [
                'validation' => $this->validator,
                'title' => 'Create New Exercise',
                'menu' => 'exercises'
            ]);
        }

        $data = [
            'org_id' => $this->session->get('org_id'),
            'exercise_title' => $this->request->getPost('exercise_title'),
            'description' => $this->request->getPost('description'),
            'status' => 'draft',
            'created_by' => $this->session->get('user_id')
        ];

        if ($this->exerciseModel->save($data)) {
            return redirect()->to('/exercise')->with('success', 'Exercise created successfully');
        }

        return redirect()->back()->with('error', 'Failed to create exercise');
    }

    /**
     * Show exercise details
     */
    public function show($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        $exercise['stats'] = $this->getExerciseStats($id);
        $exercise['creator'] = $this->exerciseModel->getExerciseWithCreator($id);

        $data = [
            'exercise' => $exercise,
            'title' => 'Exercise Details: ' . $exercise['exercise_title'],
            'menu' => 'exercises'
        ];

        return view('exercise/show', $data);
    }

    /**
     * Show edit exercise form
     */
    public function edit($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        $data = [
            'exercise' => $exercise,
            'title' => 'Edit Exercise: ' . $exercise['exercise_title'],
            'menu' => 'exercises',
            'validation' => \Config\Services::validation()
        ];

        return view('exercise/edit', $data);
    }

    /**
     * Update exercise
     */
    public function update($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        $rules = [
            'exercise_title' => 'required|max_length[255]',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return view('exercise/edit', [
                'exercise' => $exercise,
                'validation' => $this->validator,
                'title' => 'Edit Exercise: ' . $exercise['exercise_title'],
                'menu' => 'exercises'
            ]);
        }

        $data = [
            'exercise_title' => $this->request->getPost('exercise_title'),
            'description' => $this->request->getPost('description')
        ];

        if ($this->exerciseModel->update($id, $data)) {
            return redirect()->to('/exercise')->with('success', 'Exercise updated successfully');
        }

        return redirect()->back()->with('error', 'Failed to update exercise');
    }

    /**
     * Delete exercise
     */
    public function delete($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        // Check if exercise has associated data
        $stats = $this->getExerciseStats($id);
        if ($stats['applicants_count'] > 0 || $stats['positions_count'] > 0) {
            return redirect()->to('/exercise')->with('error', 'Cannot delete exercise with associated data. Archive it instead.');
        }

        if ($this->exerciseModel->delete($id)) {
            return redirect()->to('/exercise')->with('success', 'Exercise deleted successfully');
        }

        return redirect()->to('/exercise')->with('error', 'Failed to delete exercise');
    }

    /**
     * Select exercise for current session
     */
    public function select($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        $this->session->set('current_exercise_id', $id);
        $this->session->set('current_exercise', $exercise);

        return redirect()->to('/dashboard')->with('success', 'Exercise selected: ' . $exercise['exercise_title']);
    }

    /**
     * Activate exercise
     */
    public function activate($id)
    {
        return $this->updateStatus($id, 'active', 'Exercise activated successfully');
    }

    /**
     * Complete exercise
     */
    public function complete($id)
    {
        return $this->updateStatus($id, 'completed', 'Exercise completed successfully');
    }

    /**
     * Archive exercise
     */
    public function archive($id)
    {
        return $this->updateStatus($id, 'archived', 'Exercise archived successfully');
    }

    /**
     * Update exercise status
     */
    private function updateStatus($id, $status, $successMessage)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        if ($this->exerciseModel->updateStatus($id, $status, $this->session->get('user_id'))) {
            return redirect()->to('/exercise')->with('success', $successMessage);
        }

        return redirect()->to('/exercise')->with('error', 'Failed to update exercise status');
    }

    /**
     * Orphan Flush - Assign orphaned records to this exercise
     */
    public function orphanFlush($id)
    {
        $exercise = $this->exerciseModel->find($id);

        if (!$exercise || $exercise['org_id'] != $this->session->get('org_id')) {
            return redirect()->to('/exercise')->with('error', 'Exercise not found');
        }

        $orgId = $this->session->get('org_id');
        $exerciseId = $id;

        // Initialize counters
        $updatedCounts = [
            'applicants' => 0,
            'positions' => 0,
            'position_groups' => 0,
            'interviewers' => 0,
            'interview_questions' => 0,
            'selection' => 0
        ];

        try {
            $db = \Config\Database::connect();
            $db->transStart();

            // Update applicants with empty exercise_id
            $result = $db->query("UPDATE applicants SET exercise_id = ? WHERE org_id = ? AND (exercise_id IS NULL OR exercise_id = '')", [$exerciseId, $orgId]);
            $updatedCounts['applicants'] = $db->affectedRows();

            // Update positions with empty exercise_id
            $result = $db->query("UPDATE positions SET exercise_id = ? WHERE org_id = ? AND (exercise_id IS NULL OR exercise_id = '')", [$exerciseId, $orgId]);
            $updatedCounts['positions'] = $db->affectedRows();

            // Update position_groups with empty exercise_id
            $result = $db->query("UPDATE positions_groups SET exercise_id = ? WHERE org_id = ? AND (exercise_id IS NULL OR exercise_id = '')", [$exerciseId, $orgId]);
            $updatedCounts['position_groups'] = $db->affectedRows();

            // Update interviewers with empty exercise_id
            $result = $db->query("UPDATE interviewers SET exercise_id = ? WHERE position_id IN (SELECT id FROM positions WHERE org_id = ?) AND (exercise_id IS NULL OR exercise_id = '')", [$exerciseId, $orgId]);
            $updatedCounts['interviewers'] = $db->affectedRows();

            // Update interview_questions with empty exercise_id
            $result = $db->query("UPDATE interview_questions SET exercise_id = ? WHERE position_id IN (SELECT id FROM positions WHERE org_id = ?) AND (exercise_id IS NULL OR exercise_id = '')", [$exerciseId, $orgId]);
            $updatedCounts['interview_questions'] = $db->affectedRows();

            // Update selection with empty exercise_id (if table exists)
            $result = $db->query("UPDATE selection SET exercise_id = ? WHERE (exercise_id IS NULL OR exercise_id = '')", [$exerciseId]);
            $updatedCounts['selection'] = $db->affectedRows();

            $db->transComplete();

            if ($db->transStatus() === false) {
                return redirect()->to('/exercise/show/' . $id)->with('error', 'Failed to flush orphaned records');
            }

            // Calculate total updated records
            $totalUpdated = array_sum($updatedCounts);

            // Create success message
            $message = "Orphan Flush completed successfully! Updated {$totalUpdated} records:\n";
            $message .= "• Applicants: {$updatedCounts['applicants']}\n";
            $message .= "• Positions: {$updatedCounts['positions']}\n";
            $message .= "• Position Groups: {$updatedCounts['position_groups']}\n";
            $message .= "• Interviewers: {$updatedCounts['interviewers']}\n";
            $message .= "• Interview Questions: {$updatedCounts['interview_questions']}\n";
            $message .= "• Selection Records: {$updatedCounts['selection']}";

            return redirect()->to('/exercise/show/' . $id)->with('success', $message);

        } catch (\Exception $e) {
            log_message('error', 'Orphan Flush Error: ' . $e->getMessage());
            return redirect()->to('/exercise/show/' . $id)->with('error', 'An error occurred during orphan flush: ' . $e->getMessage());
        }
    }

    /**
     * Get exercise statistics
     */
    private function getExerciseStats($exerciseId)
    {
        $applicantModel = new ApplicantModel();
        $positionModel = new PositionModel();
        $interviewerModel = new InterviewerModel();
        $questionModel = new InterviewQuestionModel();

        return [
            'applicants_count' => $applicantModel->where('exercise_id', $exerciseId)->countAllResults(),
            'positions_count' => $positionModel->where('exercise_id', $exerciseId)->countAllResults(),
            'interviewers_count' => $interviewerModel->where('exercise_id', $exerciseId)->countAllResults(),
            'questions_count' => $questionModel->where('exercise_id', $exerciseId)->countAllResults()
        ];
    }
}
