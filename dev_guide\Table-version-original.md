Showing create queries
Tables
Table	Create table
applicants	CREATE TABLE `applicants` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `position_id` int(11) NOT NULL,
 `position_group_id` int(11) unsigned DEFAULT NULL,
 `name` varchar(255) NOT NULL,
 `sex` varchar(100) NOT NULL,
 `age` int(3) NOT NULL,
 `place_origin` varchar(255) NOT NULL,
 `contact_details` varchar(255) DEFAULT NULL,
 `nid_number` varchar(100) DEFAULT NULL,
 `current_employer` varchar(255) DEFAULT NULL,
 `current_position` varchar(255) DEFAULT NULL,
 `address_location` varchar(255) NOT NULL,
 `qualification_text` text NOT NULL,
 `other_trainings` text DEFAULT NULL,
 `knowledge` text NOT NULL,
 `skills_competencies` text NOT NULL,
 `job_experiences` text NOT NULL,
 `publications` text DEFAULT NULL,
 `awards` text DEFAULT NULL,
 `referees` text DEFAULT NULL,
 `comments` text DEFAULT NULL,
 `rate_age` int(2) DEFAULT NULL,
 `rate_qualification` int(2) DEFAULT NULL,
 `rate_experience` int(2) DEFAULT NULL,
 `rate_trainings` int(2) DEFAULT NULL,
 `rate_skills_competencies` int(2) DEFAULT NULL,
 `rate_knowledge` int(2) DEFAULT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `created_by` varchar(255) NOT NULL,
 `updated_by` varchar(255) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `max_rate_age` int(5) DEFAULT NULL,
 `max_rate_qualification` int(5) DEFAULT NULL,
 `rate_private_non_relevant` int(5) DEFAULT NULL,
 `max_rate_private_non_relevant` int(5) DEFAULT NULL,
 `rate_private_relevant` int(5) DEFAULT NULL,
 `max_rate_private_relevant` int(5) DEFAULT NULL,
 `rate_public_non_relevant` int(5) DEFAULT NULL,
 `max_rate_public_non_relevant` int(5) DEFAULT NULL,
 `rate_public_relevant` int(5) DEFAULT NULL,
 `max_rate_public_relevant` int(5) DEFAULT NULL,
 `max_rate_experience` int(5) DEFAULT NULL,
 `max_rate_trainings` int(5) DEFAULT NULL,
 `max_rate_skills_competencies` int(5) DEFAULT NULL,
 `max_rate_knowledge` int(5) DEFAULT NULL,
 `rate_public_service` int(5) DEFAULT NULL,
 `max_rate_public_service` int(5) DEFAULT NULL,
 `rate_capability` int(5) DEFAULT NULL,
 `max_rate_capability` int(5) DEFAULT NULL,
 `remarks` text NOT NULL,
 `application_status` varchar(100) NOT NULL,
 `app_status_reason` text NOT NULL,
 `app_status_file` varchar(255) NOT NULL,
 `rate_total` int(5) DEFAULT NULL,
 `max_rate_total` int(5) DEFAULT NULL,
 `pre_select_status` varchar(255) DEFAULT NULL,
 `interview_notices` text NOT NULL,
 PRIMARY KEY (`id`),
 KEY `org_id_index` (`org_id`),
 KEY `position_id_index` (`position_id`),
 KEY `created_by_index` (`created_by`),
 KEY `updated_by_index` (`updated_by`),
 KEY `fk_applicant_position_group_id` (`position_group_id`),
 CONSTRAINT `fk_applicant_position_group_id` FOREIGN KEY (`position_group_id`) REFERENCES `positions_groups` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4306 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_org	CREATE TABLE `dakoii_org` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `orgcode` varchar(500) NOT NULL,
 `name` varchar(255) NOT NULL,
 `description` text NOT NULL,
 `is_active` tinyint(1) DEFAULT 1,
 `license_status` varchar(50) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `advertisement_no` varchar(100) DEFAULT NULL,
 `advertisement_date` date DEFAULT NULL,
 `mode_of_advert` varchar(100) DEFAULT NULL,
 `org_logo` varchar(255) DEFAULT NULL,
 `sort_out_of` int(11) DEFAULT NULL,
 `introduction` text DEFAULT NULL,
 `composition` text DEFAULT NULL,
 `criteria` text DEFAULT NULL,
 `culling` text DEFAULT NULL,
 `ai_model` varchar(20) DEFAULT NULL,
 `interview_settings` text DEFAULT NULL COMMENT 'save data in json',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `orgcode` varchar(500) NOT NULL,
 `role` varchar(100) NOT NULL,
 `is_active` tinyint(1) DEFAULT 0,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
exercises	CREATE TABLE `exercises` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(10) unsigned NOT NULL,
 `exercise_title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `status` enum('draft','active','completed','archived') NOT NULL DEFAULT 'draft',
 `status_by` int(10) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `created_by` int(10) unsigned NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
interviewers	CREATE TABLE `interviewers` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `position_id` int(11) NOT NULL,
 `interviewer_name` varchar(255) NOT NULL,
 `interviewer_position` varchar(255) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
interview_data	CREATE TABLE `interview_data` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `position_id` int(11) NOT NULL,
 `interviewer_id` int(11) NOT NULL,
 `applicant_id` int(11) NOT NULL,
 `question_id` int(11) NOT NULL,
 `score` varchar(5) DEFAULT NULL,
 `comments` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2240 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
interview_questions	CREATE TABLE `interview_questions` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `position_id` int(11) NOT NULL,
 `question_no` int(11) NOT NULL,
 `question_text` text NOT NULL,
 `set_score` decimal(5,2) DEFAULT 0.00,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
positions	CREATE TABLE `positions` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `position_no` varchar(50) NOT NULL,
 `designation` varchar(255) NOT NULL,
 `classification` varchar(100) NOT NULL,
 `award` varchar(100) NOT NULL,
 `qualifications` text NOT NULL,
 `knowledge` text NOT NULL,
 `skills_competencies` text NOT NULL,
 `job_experiences` text NOT NULL,
 `org_id` int(11) NOT NULL,
 `position_group_id` int(11) unsigned DEFAULT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `remarks` text DEFAULT NULL,
 `created_by` varchar(255) NOT NULL,
 `updated_by` varchar(255) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `for_interview` varchar(20) NOT NULL COMMENT 'for interview status',
 PRIMARY KEY (`id`),
 UNIQUE KEY `position_no_unique` (`position_no`),
 KEY `org_id_index` (`org_id`),
 KEY `created_by_index` (`created_by`),
 KEY `updated_by_index` (`updated_by`),
 KEY `fk_position_group_id` (`position_group_id`),
 CONSTRAINT `fk_position_group_id` FOREIGN KEY (`position_group_id`) REFERENCES `positions_groups` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=560 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
positions_groups	CREATE TABLE `positions_groups` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `priority` int(11) DEFAULT NULL,
 `group_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `created_by` varchar(255) NOT NULL,
 `updated_by` varchar(255) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`),
 UNIQUE KEY `group_name_unique` (`group_name`,`org_id`),
 KEY `org_id_index` (`org_id`),
 KEY `created_by_index` (`created_by`),
 KEY `updated_by_index` (`updated_by`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
selection	CREATE TABLE `selection` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `box` varchar(20) NOT NULL,
 `value` varchar(200) NOT NULL,
 `item` varchar(200) NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
users	CREATE TABLE `users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` varchar(100) NOT NULL,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` enum('admin','moderator','user') NOT NULL DEFAULT 'user',
 `is_active` tinyint(1) NOT NULL DEFAULT 0,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
