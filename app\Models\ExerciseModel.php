<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table      = 'exercises';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    // Fields that are allowed to be inserted/updated
    protected $allowedFields = [
        'org_id',
        'exercise_title',
        'description',
        'status',
        'status_by',
        'status_at',
        'created_by'
    ];

    // Automatically handle the timestamps for created_at and updated_at
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Optional: Default values for specific fields
    protected $defaultValues = [
        'status' => 'draft'
    ];

    // Validation rules for the exercise model
    protected $validationRules = [
        'org_id' => 'required|integer',
        'exercise_title' => 'required|max_length[255]',
        'status' => 'in_list[draft,active,completed,archived]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'integer' => 'Organization ID must be a valid integer'
        ],
        'exercise_title' => [
            'required' => 'Exercise title is required',
            'max_length' => 'Exercise title cannot exceed 255 characters'
        ],
        'status' => [
            'in_list' => 'Status must be one of: draft, active, completed, archived'
        ],
        'created_by' => [
            'required' => 'Created by user ID is required',
            'integer' => 'Created by must be a valid integer'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getExercisesByOrg($orgId)
    {
        return $this->where('org_id', $orgId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get active exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getActiveExercisesByOrg($orgId)
    {
        return $this->where('org_id', $orgId)
                    ->where('status', 'active')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get exercises by status
     *
     * @param string $status
     * @param int|null $orgId
     * @return array
     */
    public function getExercisesByStatus($status, $orgId = null)
    {
        $builder = $this->where('status', $status);
        
        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Update exercise status
     *
     * @param int $exerciseId
     * @param string $status
     * @param int $userId
     * @return bool
     */
    public function updateStatus($exerciseId, $status, $userId)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s')
        ];

        return $this->update($exerciseId, $data);
    }

    /**
     * Get exercise with creator information
     *
     * @param int $exerciseId
     * @return array|null
     */
    public function getExerciseWithCreator($exerciseId)
    {
        return $this->select('exercises.*, users.name as creator_name, users.username as creator_username')
                    ->join('users', 'users.id = exercises.created_by', 'left')
                    ->where('exercises.id', $exerciseId)
                    ->first();
    }
}
