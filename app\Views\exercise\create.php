<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-plus mr-2"></i>
                        <?= $title ?>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('exercise') ?>">Exercises</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tasks mr-2"></i>
                                Create New Exercise
                            </h3>
                        </div>
                        
                        <?= form_open('exercise/store', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="card-body">
                            <!-- Exercise Title -->
                            <div class="form-group">
                                <label for="exercise_title" class="required">Exercise Title</label>
                                <input type="text" 
                                       class="form-control <?= $validation->hasError('exercise_title') ? 'is-invalid' : '' ?>" 
                                       id="exercise_title" 
                                       name="exercise_title" 
                                       value="<?= old('exercise_title') ?>" 
                                       placeholder="Enter exercise title (e.g., 2024 Q1 Recruitment)"
                                       required>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('exercise_title') ?>
                                </div>
                                <small class="form-text text-muted">
                                    Choose a descriptive title that identifies this recruitment round
                                </small>
                            </div>

                            <!-- Description -->
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control <?= $validation->hasError('description') ? 'is-invalid' : '' ?>" 
                                          id="description" 
                                          name="description" 
                                          rows="4" 
                                          placeholder="Enter exercise description (optional)"><?= old('description') ?></textarea>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('description') ?>
                                </div>
                                <small class="form-text text-muted">
                                    Provide additional details about this exercise (optional)
                                </small>
                            </div>

                            <!-- Information Box -->
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info-circle"></i> Exercise Information</h5>
                                <ul class="mb-0">
                                    <li>New exercises are created in <strong>Draft</strong> status</li>
                                    <li>You can activate the exercise when ready to start recruitment</li>
                                    <li>All data (applicants, positions, interviewers) will be associated with this exercise</li>
                                    <li>You can switch between exercises at any time</li>
                                </ul>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="<?= base_url('exercise') ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left mr-1"></i>
                                        Back to Exercises
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-1"></i>
                                        Create Exercise
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
.required:after {
    content: " *";
    color: red;
}
</style>

<?= $this->endSection() ?>
