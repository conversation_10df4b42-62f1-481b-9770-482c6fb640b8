<?php

namespace App\Controllers;

use App\Models\PositionsGroupModel;
use App\Models\PositionModel;
use App\Models\ApplicantModel;
use App\Models\DakoiiOrgModel;

class Reports extends BaseController
{
    protected $positionsGroupModel;
    protected $positionModel;
    protected $applicantModel;
    protected $dakoiiOrgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info', 'exercise', 'exercise_settings']);
        $this->positionsGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionModel();
        $this->applicantModel = new ApplicantModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }

    public function index()
    {
        $data['title'] = 'Reports Dashboard';
        $data['menu'] = 'reports';
        $orgId = session('org_id');

        $positionGroups = $this->positionsGroupModel->where('org_id', $orgId)
            ->orderBy('group_name', 'ASC')
            ->findAll();

        $totalApplicants = 0;
        $totalPositions = 0;
        $totalRatedApplicants = 0;
        $totalRatedPositions = 0;

        foreach ($positionGroups as &$group) {
            $group['positions'] = $this->positionModel->where('position_group_id', $group['id'])
                ->where('org_id', $orgId)
                ->orderBy('position_no', 'ASC')
                ->findAll();
            $group['total_applicants'] = 0;
            $group['all_applicants_count'] = 0;
            $group['total_rated_applicants'] = 0;
            $group['rated_positions'] = 0;
            $group['needs_generation'] = $this->checkNeedsGeneration($group['id']);

            // Reset eliminated and shortlisted counts for this group
            $group['eliminated_count'] = 0;
            $group['shortlisted_count'] = 0;
            $group['withdrawn_count'] = 0;
            $group['blank_status_count'] = 0;

            // Get all position IDs in this group for direct count queries
            $positionIds = array_column($group['positions'], 'id');
            if (!empty($positionIds)) {
                // Direct DB count for all eliminated applicants in this position group
                $group['eliminated_count'] = $this->applicantModel
                    ->whereIn('position_id', $positionIds)
                    ->where('org_id', $orgId)
                    ->where('application_status', 'Eliminated')
                    ->countAllResults();

                // Direct DB count for all shortlisted applicants in this position group
                $group['shortlisted_count'] = $this->applicantModel
                    ->whereIn('position_id', $positionIds)
                    ->where('org_id', $orgId)
                    ->where('application_status', 'Shortlisted')
                    ->countAllResults();

                // Direct DB count for all withdrawn applicants in this position group
                $group['withdrawn_count'] = $this->applicantModel
                    ->whereIn('position_id', $positionIds)
                    ->where('org_id', $orgId)
                    ->where('application_status', 'Withdrawn')
                    ->countAllResults();

                // Direct DB count for all blank status applicants in this position group
                $group['blank_status_count'] = $this->applicantModel
                    ->whereIn('position_id', $positionIds)
                    ->where('org_id', $orgId)
                    ->groupStart()
                        ->where('application_status', '')
                        ->orWhere('application_status', null)
                    ->groupEnd()
                    ->countAllResults();

                // Direct DB count for ALL applicants in this position group
                $group['all_applicants_count'] = $this->applicantModel
                    ->whereIn('position_id', $positionIds)
                    ->where('org_id', $orgId)
                    ->countAllResults();
            }

            foreach ($group['positions'] as &$position) {
                // Get all applicants for this position (regardless of status)
                $allApplicants = $this->applicantModel
                    ->where('position_id', $position['id'])
                    ->where('org_id', $orgId)
                    ->findAll();

                // Count total applicants including eliminated ones
                $position['all_applicants_count'] = count($allApplicants);

                // Get non-eliminated applicants for rating progress calculations
                $applicants = $this->applicantModel
                    ->where('position_id', $position['id'])
                    ->where('org_id', $orgId)
                    ->where('application_status !=', 'Eliminated')
                    ->orderBy('name', 'ASC')
                    ->findAll();

                $position['total_applicants'] = count($applicants);
                $position['rated_applicants'] = count(array_filter($applicants, function ($a) {
                    return $a['rate_qualification'] > 0;
                }));

                // Add to group total of non-eliminated applicants (for rating progress)
                $group['total_applicants'] += $position['total_applicants'];
                $group['total_rated_applicants'] += $position['rated_applicants'];

                if ($position['total_applicants'] > 0 && $position['total_applicants'] == $position['rated_applicants']) {
                    $group['rated_positions']++;
                    $totalRatedPositions++;
                }
            }



            $totalApplicants += $group['total_applicants'];
            $totalPositions += count($group['positions']);
            $totalRatedApplicants += $group['total_rated_applicants'];

            $group['rating_progress'] = $group['total_applicants'] > 0 ? ($group['total_rated_applicants'] / $group['total_applicants']) * 100 : 0;
        }


        //get total applicants in the organization
        $totalApplicants = $this->applicantModel->where('org_id', $orgId)->countAllResults();

        //get total rated applicants in the organization
        $totalRatedApplicants = $this->applicantModel->where('org_id', $orgId)->where('rate_qualification !=', 0)->countAllResults();

        // Get total eliminated and shortlisted counts for the entire organization using direct DB queries
        $totalEliminatedCount = $this->applicantModel->where('org_id', $orgId)
            ->where('application_status', 'Eliminated')
            ->countAllResults();

        $totalShortlistedCount = $this->applicantModel->where('org_id', $orgId)
            ->where('application_status', 'Shortlisted')
            ->countAllResults();

        // Get total withdrawn and blank status counts for the entire organization
        $totalWithdrawnCount = $this->applicantModel->where('org_id', $orgId)
            ->where('application_status', 'Withdrawn')
            ->countAllResults();

        $totalBlankStatusCount = $this->applicantModel->where('org_id', $orgId)
            ->groupStart()
                ->where('application_status', '')
                ->orWhere('application_status', null)
            ->groupEnd()
            ->countAllResults();

        $data['positionGroups'] = $positionGroups;
        $data['totalApplicants'] = $totalApplicants;
        $data['totalRatedApplicants'] = $totalRatedApplicants;
        $data['totalPositions'] = $totalPositions;
        $data['totalRatedPositions'] = $totalRatedPositions;
        $data['totalEliminatedCount'] = $totalEliminatedCount;
        $data['totalShortlistedCount'] = $totalShortlistedCount;
        $data['totalWithdrawnCount'] = $totalWithdrawnCount;
        $data['totalBlankStatusCount'] = $totalBlankStatusCount;
        $data['ratingProgress'] = $totalApplicants > 0 ? ($totalRatedApplicants / $totalApplicants) * 100 : 0;

        // Get top 3 highest rated applicants
        $data['topApplicants'] = $this->applicantModel
            ->select('applicants.*, positions.designation as position_title, (rate_age + rate_qualification + rate_experience + rate_trainings + rate_skills_competencies + rate_knowledge + rate_public_service) as total_rating')
            ->join('positions', 'positions.id = applicants.position_id')
            ->where('applicants.org_id', $orgId)
            ->where('rate_qualification !=', 0)
            ->orderBy('total_rating', 'DESC')
            ->limit(3)
            ->find();

        // Calculate rating distribution
        $allApplicants = $this->applicantModel->where('org_id', $orgId)->findAll();
        $data['ratingDistribution'] = [
            count(array_filter($allApplicants, function ($a) {
                return $this->calculateTotalRating($a) >= 80;
            })),
            count(array_filter($allApplicants, function ($a) {
                return $this->calculateTotalRating($a) >= 60 && $this->calculateTotalRating($a) < 80;
            })),
            count(array_filter($allApplicants, function ($a) {
                return $this->calculateTotalRating($a) >= 40 && $this->calculateTotalRating($a) < 60;
            })),
            count(array_filter($allApplicants, function ($a) {
                return $this->calculateTotalRating($a) >= 20 && $this->calculateTotalRating($a) < 40;
            })),
            count(array_filter($allApplicants, function ($a) {
                return $this->calculateTotalRating($a) < 20;
            }))
        ];

        $data['applicants_count'] = $this->applicantModel->where('org_id', session('org_id'))->countAllResults();

        // Prepare data for Applicants per Position Group Chart
        $data['applicantsPerGroup'] = array_column($positionGroups, 'all_applicants_count');
        $data['groupNames'] = array_column($positionGroups, 'group_name');

        return view('reports/index', $data);
    }

    private function calculateTotalRating($applicant)
    {
        return $applicant['rate_age'] + $applicant['rate_qualification'] + $applicant['rate_experience'] +
            $applicant['rate_trainings'] + $applicant['rate_skills_competencies'] +
            $applicant['rate_knowledge'] + $applicant['rate_public_service'];
    }

    public function viewPositions($groupId)
    {
        $data['title'] = 'View Positions';
        $data['menu'] = 'reports';
        $positions = $this->positionModel->where('position_group_id', $groupId)
            ->where('org_id', session('org_id'))
            ->orderBy('position_no', 'ASC')
            ->findAll();

        foreach ($positions as &$position) {
            $position['applicants_count'] = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', session('org_id'))
                ->countAllResults();
            $position['rated_applicants_count'] = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', session('org_id'))
                ->where('rate_qualification !=', 0)
                ->countAllResults();
        }

        $data['positions'] = $positions;
        $data['groupName'] = $this->positionsGroupModel->where('org_id', session('org_id'))->find($groupId)['group_name'];
        $data['groupId'] = $groupId; // Add this line to pass the groupId to the view
        return view('reports/view_positions', $data);
    }

    public function interviewList($positionId)
    {
        $data['title'] = 'Interview List';
        $data['menu'] = 'reports';

        // Get position details
        $data['position'] = $this->positionModel->where('org_id', session('org_id'))->find($positionId);

        if (!$data['position']) {
            return redirect()->to(base_url('reports'))->with('error', 'Position not found.');
        }

        // Get shortlisted applicants
        $data['applicants'] = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', session('org_id'))
            ->where('application_status', 'Shortlisted')
            ->orderBy('name', 'ASC')
            ->findAll();

        return view('reports/interview_list', $data);
    }

    public function applicantProfiles($positionId)
    {
        $data['title'] = 'Applicant Profiles';
        $data['menu'] = 'reports';

        $data['position'] = $this->positionModel->where('org_id', session('org_id'))->find($positionId);

        if (!$data['position']) {
            return redirect()->to(base_url('reports'))->with('error', 'Position not found.');
        }

        // Get all applicants for this position
        $applicants = $this->applicantModel->where('position_id', $positionId)
            ->where('org_id', session('org_id'))
            ->findAll();

        // Sort applicants by priority status and then by rating
        $shortlisted = [];
        $eliminated = [];
        $withdrawn = [];
        $others = [];

        foreach ($applicants as $applicant) {
            switch ($applicant['application_status']) {
                case 'Shortlisted':
                    $shortlisted[] = $applicant;
                    break;
                case 'Eliminated':
                    $eliminated[] = $applicant;
                    break;
                case 'Withdrawn':
                    $withdrawn[] = $applicant;
                    break;
                default:
                    $others[] = $applicant;
                    break;
            }
        }

        // Sort each group by rate_total in descending order
        $sortByRating = function ($a, $b) {
            return $b['rate_total'] - $a['rate_total'];
        };

        usort($shortlisted, $sortByRating);
        usort($eliminated, $sortByRating);
        usort($withdrawn, $sortByRating);
        usort($others, $sortByRating);

        // Combine all groups in the required order
        $data['applicants'] = array_merge($shortlisted, $eliminated, $withdrawn, $others);

        // Fetch organization data
        $orgId = session('org_id');
        $data['org'] = $this->dakoiiOrgModel->find($orgId);

        // Set base title
        $data['title'] = $data['position']['position_no'] . ' RS3-7 ';

        // Add page number suffix to title if page parameter exists
        $currentPage = $this->request->getGet('page') ? (int)$this->request->getGet('page') : 1;
        if ($currentPage > 0) {
            $data['title'] .= '-' . $currentPage;
        }

        return view('reports/applicant_profiles', $data);
    }

    private function checkNeedsGeneration($groupId)
    {
        $positionModel = new PositionModel();
        $applicantModel = new ApplicantModel();

        $positions = $positionModel->where('position_group_id', $groupId)->findAll();

        foreach ($positions as $position) {
            $applicants = $applicantModel->where('position_id', $position['id'])->findAll();
            foreach ($applicants as $applicant) {
                if ($applicant['rate_total'] == 0) {
                    return true;
                }
            }
        }

        return false;
    }

    public function listEliminatedApplicants($groupId, $positionId = null)
    {
        $data['title'] = 'Eliminated Applicants';
        $data['menu'] = 'reports';

        if ($positionId) {
            $positions = [$this->positionModel->find($positionId)];
        } else {
            $positions = $this->positionModel->where('position_group_id', $groupId)
                ->where('org_id', session('org_id'))
                ->findAll();
        }

        $eliminatedApplicants = [];
        foreach ($positions as $position) {
            $applicants = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', session('org_id'))
                ->where('application_status', 'Eliminated')
                ->findAll();
            foreach ($applicants as $applicant) {
                $applicant['position'] = $position['designation'];
                $applicant['position_no'] = $position['position_no'];
                $eliminatedApplicants[] = $applicant;
            }
        }

        $data['eliminatedApplicants'] = $eliminatedApplicants;
        $data['groupName'] = $this->positionsGroupModel->where('org_id', session('org_id'))->find($groupId)['group_name'];
        $data['positionNo'] = $positionId ? $positions[0]['position_no'] : 'All Positions';
        $data['positionName'] = $positionId ? $positions[0]['designation'] : 'All Positions';
        $data['groupId'] = $groupId; // Add this line
        return view('reports/eliminated_applicants', $data);
    }

    public function listApplicantsByRank($groupId, $positionId = null)
    {
        $data['title'] = "";
        $data['menu'] = 'reports';

        if ($positionId) {
            $positions = [$this->positionModel->find($positionId)];
            $data['title'] =  $positions[0]['position_no'] . ' - Rank - ' . $positions[0]['designation'];
        } else {
            $positions = $this->positionModel->where('position_group_id', $groupId)
                ->where('org_id', session('org_id'))
                ->findAll();
        }

        $rankedApplicants = [];
        foreach ($positions as $position) {
            $applicants = $this->applicantModel->where('position_id', $position['id'])
                ->where('org_id', session('org_id'))
                //->where('application_status !=', 'Eliminated')
                ->orderBy('rate_total', 'DESC')
                ->findAll();
            foreach ($applicants as $index => $applicant) {
                $applicant['position'] = $position['designation'];
                $applicant['position_no'] = $position['position_no'];
                $applicant['rank'] = $index + 1;
                $rankedApplicants[] = $applicant;
            }
        }

        // Sort all applicants by total rating
        usort($rankedApplicants, function ($a, $b) {
            return $b['rate_total'] - $a['rate_total'];
        });

        // Reassign overall rank
        foreach ($rankedApplicants as $index => $applicant) {
            $rankedApplicants[$index]['overall_rank'] = $index + 1;
        }

        //get organization data
        $data['org'] = $this->dakoiiOrgModel->where('id', session('org_id'))->first();
        $data['rankedApplicants'] = $rankedApplicants;
        $data['groupName'] = $this->positionsGroupModel->where('org_id', session('org_id'))->find($groupId)['group_name'];
        $data['positionNo'] = $positionId ? $positions[0]['position_no'] : 'All Positions';
        $data['positionName'] = $positionId ? $positions[0]['designation'] : 'All Positions';
        $data['groupId'] = $groupId; // Add this line
        return view('reports/applicants_by_rank', $data);
    }

    public function preSelectionReport()
    {
        $data['title'] = 'Pre-Selection Report';
        $data['menu'] = 'reports';

        // Fetch organization data
        $orgId = session('org_id');
        $data['org'] = $this->dakoiiOrgModel->find($orgId);

        // Fetch all position groups for the organization, ordered by priority
        $data['positionGroups'] = $this->positionsGroupModel->where('org_id', $orgId)
                                                           ->orderBy('priority', 'ASC')
                                                           ->findAll();

        // Initialize arrays to store summary data
        $data['totalApplicants'] = 0;
        $data['shortlistedCount'] = 0;
        $data['eliminatedCount'] = 0;
        $data['withdrawnCount'] = 0;

        // Fetch and calculate data for each position group
        foreach ($data['positionGroups'] as &$group) {
            $positions = $this->positionModel->where('position_group_id', $group['id'])->findAll();
            $group['positions'] = [];

            foreach ($positions as $position) {
                $applicants = $this->applicantModel->where('position_id', $position['id'])->findAll();
                $position['totalApplicants'] = count($applicants);
                $position['shortlistedCount'] = count(array_filter($applicants, function($a) { return $a['application_status'] == 'Shortlisted'; }));
                $position['eliminatedCount'] = count(array_filter($applicants, function($a) { return $a['application_status'] == 'Eliminated'; }));
                $position['withdrawnCount'] = count(array_filter($applicants, function($a) { return $a['application_status'] == 'Withdrawn'; }));

                $group['positions'][] = $position;

                // Update total counts
                $data['totalApplicants'] += $position['totalApplicants'];
                $data['shortlistedCount'] += $position['shortlistedCount'];
                $data['eliminatedCount'] += $position['eliminatedCount'];
                $data['withdrawnCount'] += $position['withdrawnCount'];
            }
        }

        $data['remarks'] = 'The selection process was conducted fairly and transparently.';

        return view('reports/pre_selection_report', $data);
    }

    public function saveRemarks()
    {
        // Check if the request is AJAX
        if ($this->request->is('post')) {
            $positionId = $this->request->getPost('positionId');
            $remarks = $this->request->getPost('remarks');

            // Validate the input
            if (!$positionId || !$remarks) {
                return redirect()->back()->with('error', 'Invalid input');
            }

            // Update the position with the new remarks
            $positionModel = new PositionModel();
            $updated = $positionModel->update($positionId, ['remarks' => $remarks]);

            if ($updated) {
                return redirect()->back()->with('success', 'Remarks saved successfully');
            } else {
                return redirect()->back()->with('error', 'Failed to save remarks');
            }
        }


    }

    public function analysisReport($groupId, $positionId)
    {
        $positionModel = new \App\Models\PositionModel();
        $applicantModel = new \App\Models\ApplicantModel();

        // Get position details
        $position = $positionModel->find($positionId);
        if (!$position) {
            return redirect()->back()->with('error', 'Position not found');
        }

        // Get all applicants for this position with their ratings
        $applicants = $applicantModel->where('position_id', $positionId)
                                    ->findAll();

        // Calculate total scores and add to applicants array
        foreach ($applicants as &$applicant) {
            $experienceScore =
                ($applicant['rate_private_non_relevant'] ?? 0) +
                ($applicant['rate_private_relevant'] ?? 0) +
                ($applicant['rate_public_non_relevant'] ?? 0) +
                ($applicant['rate_public_relevant'] ?? 0);

            $applicant['total_score'] =
                ($applicant['rate_age'] ?? 0) +
                ($applicant['rate_qualification'] ?? 0) +
                ($applicant['rate_trainings'] ?? 0) +
                ($applicant['rate_skills_competencies'] ?? 0) +
                ($applicant['rate_knowledge'] ?? 0) +
                ($applicant['rate_public_service'] ?? 0) +
                ($applicant['rate_private_non_relevant'] ?? 0) +
                ($applicant['rate_private_relevant'] ?? 0) +
                ($applicant['rate_public_non_relevant'] ?? 0) +
                ($applicant['rate_public_relevant'] ?? 0) +
                ($applicant['rate_capability'] ?? 0);
        }

        // Sort applicants by total score in descending order
        usort($applicants, function($a, $b) {
            return $b['total_score'] <=> $a['total_score'];
        });

        $data = [
            'title' => $position['position_no'] . ' - Analysis Report' ,
            'menu' => 'reports',
            'position' => $position,
            'applicants' => $applicants
        ];

        return view('reports/list_applicants_analysis', $data);
    }

    public function viewApplicantAnalysis($applicantId)
    {
        $applicantModel = new \App\Models\ApplicantModel();
        $positionModel = new \App\Models\PositionModel();

        // Get applicant details with ratings
        $applicant = $applicantModel->find($applicantId);
        if (!$applicant) {
            return redirect()->back()->with('error', 'Applicant not found');
        }

        // Get position details using applicant's position_id
        $position = $positionModel->find($applicant['position_id']);
        if (!$position) {
            return redirect()->back()->with('error', 'Position not found');
        }

        // Calculate total scores
        $experienceScore =
            ($applicant['rate_private_non_relevant'] ?? 0) +
            ($applicant['rate_private_relevant'] ?? 0) +
            ($applicant['rate_public_non_relevant'] ?? 0) +
            ($applicant['rate_public_relevant'] ?? 0);

        $maxExperienceScore =
            ($applicant['max_rate_private_non_relevant'] ?? 0) +
            ($applicant['max_rate_private_relevant'] ?? 0) +
            ($applicant['max_rate_public_non_relevant'] ?? 0) +
            ($applicant['max_rate_public_relevant'] ?? 0);

        $totalScore =
            ($applicant['rate_age'] ?? 0) +
            ($applicant['rate_qualification'] ?? 0) +
            ($applicant['rate_trainings'] ?? 0) +
            ($applicant['rate_skills_competencies'] ?? 0) +
            ($applicant['rate_knowledge'] ?? 0) +
            ($applicant['rate_public_service'] ?? 0) +
            $experienceScore +
            ($applicant['rate_capability'] ?? 0);

        $maxTotalScore =
            ($applicant['max_rate_age'] ?? 0) +
            ($applicant['max_rate_qualification'] ?? 0) +
            ($applicant['max_rate_trainings'] ?? 0) +
            ($applicant['max_rate_skills_competencies'] ?? 0) +
            ($applicant['max_rate_knowledge'] ?? 0) +
            ($applicant['max_rate_public_service'] ?? 0) +
            $maxExperienceScore +
            ($applicant['max_rate_capability'] ?? 0);

        // Calculate total score for sorting
        $totalScore = ($applicant['rate_age'] ?? 0) +
            ($applicant['rate_qualification'] ?? 0) +
            ($applicant['rate_trainings'] ?? 0) +
            ($applicant['rate_skills_competencies'] ?? 0) +
            ($applicant['rate_knowledge'] ?? 0) +
            ($applicant['rate_public_service'] ?? 0) +
            ($applicant['rate_private_non_relevant'] ?? 0) +
            ($applicant['rate_private_relevant'] ?? 0) +
            ($applicant['rate_public_non_relevant'] ?? 0) +
            ($applicant['rate_public_relevant'] ?? 0) +
            ($applicant['rate_capability'] ?? 0);

        // Get all applicants for this position to show ranking
        $allApplicants = $this->applicantModel
            ->where('position_id', $applicant['position_id'])
            ->where('rate_qualification >', 0)
            ->findAll();

        // Calculate total score for each applicant
        foreach ($allApplicants as &$app) {
            $app['total_score'] =
                ($app['rate_age'] ?? 0) +
                ($app['rate_qualification'] ?? 0) +
                ($app['rate_trainings'] ?? 0) +
                ($app['rate_skills_competencies'] ?? 0) +
                ($app['rate_knowledge'] ?? 0) +
                ($app['rate_public_service'] ?? 0) +
                ($app['rate_private_non_relevant'] ?? 0) +
                ($app['rate_private_relevant'] ?? 0) +
                ($app['rate_public_non_relevant'] ?? 0) +
                ($app['rate_public_relevant'] ?? 0) +
                ($app['rate_capability'] ?? 0);
        }

        // Sort applicants by total score in descending order
        usort($allApplicants, function($a, $b) {
            return $b['total_score'] <=> $a['total_score']; // Using spaceship operator for better comparison
        });

        // Find rank of current applicant
        $rank = 1;
        foreach ($allApplicants as $index => $app) {
            if ($app['id'] == $applicantId) {
                break;
            }
            $rank++;
        }

        $data = [
            'title' => 'Applicant Analysis Report',
            'menu' => 'reports',
            'position' => $position,
            'applicant' => $applicant,
            'experienceScore' => $experienceScore,
            'maxExperienceScore' => $maxExperienceScore,
            'totalScore' => $totalScore,
            'maxTotalScore' => $maxTotalScore,
            'rank' => $rank,
            'totalApplicants' => count($allApplicants),
            'allApplicants' => $allApplicants
        ];

        return view('reports/view_applicant_analysis', $data);
    }

    public function bulk_shortlist()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicantIds = explode(',', $this->request->getPost('applicantIds'));
        $reason = $this->request->getPost('shortlistReason');

        if (empty($applicantIds) || empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Missing required parameters']);
        }

        try {
            foreach ($applicantIds as $id) {
                $this->applicantModel->update($id, [
                    'application_status' => 'Shortlisted',
                    'app_status_reason' => $reason
                ]);
            }

            return $this->response->setJSON(['success' => true, 'message' => 'Applicants shortlisted successfully']);
        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Failed to shortlist applicants']);
        }
    }

    public function bulk_eliminate()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicantIds = explode(',', $this->request->getPost('applicantIds'));
        $reason = $this->request->getPost('eliminateReason');

        if (empty($applicantIds) || empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Missing required parameters']);
        }

        try {
            foreach ($applicantIds as $id) {
                $this->applicantModel->update($id, [
                    'application_status' => 'Eliminated',
                    'app_status_reason' => $reason
                ]);
            }

            return $this->response->setJSON(['success' => true, 'message' => 'Applicants eliminated successfully']);
        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Failed to eliminate applicants']);
        }
    }

    public function bulk_withdraw()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request method']);
        }

        $applicantIds = explode(',', $this->request->getPost('applicantIds'));
        $reason = $this->request->getPost('withdrawReason');

        if (empty($applicantIds) || empty($reason)) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Missing required parameters']);
        }

        try {
            foreach ($applicantIds as $id) {
                $this->applicantModel->update($id, [
                    'application_status' => 'Withdrawn',
                    'app_status_reason' => $reason
                ]);
            }

            return $this->response->setJSON(['success' => true, 'message' => 'Applicants withdrawn successfully']);
        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Failed to withdraw applicants']);
        }
    }

    public function save_interview_settings()
    {
        // ... existing code ...
    }

    /**
     * Generate and display interview schedule for shortlisted applicants
     */
    public function interviewSchedule()
    {
        // Ensure exercise context exists
        require_exercise_context();

        $data['title'] = 'Interview Schedule';
        $data['menu'] = 'reports';
        $exerciseId = get_current_exercise_id();
        $orgId = session('org_id');

        // Get interview settings from exercise settings (not organization)
        $interviewSettings = get_interview_settings();

        // If no settings exist, try to migrate from organization settings
        if (empty($interviewSettings)) {
            $migrated = migrate_org_settings_to_exercise();
            if ($migrated > 0) {
                $interviewSettings = get_interview_settings();
            }
        }

        // Ensure interviewSettings is an array
        if (!is_array($interviewSettings)) {
            $interviewSettings = json_decode($interviewSettings ?? '{}', true) ?: [];
        }

        // Check if interview settings are configured
        if (empty($interviewSettings)) {
            return redirect()->to('settings/interviews')->with('error', 'Please configure interview settings first.');
        }

        // Extract interview settings
        $minutesPerInterview = $interviewSettings['minutes_per_interview'] ?? 30;
        $transitionTime = $interviewSettings['transition_time'] ?? 5;
        $startTime = $interviewSettings['start_time'] ?? '08:00';
        $endTime = $interviewSettings['end_time'] ?? '16:00';
        $startDate = $interviewSettings['start_date'] ?? date('Y-m-d');
        $breakTimes = $interviewSettings['break_times'] ?? [];
        $breakDays = $interviewSettings['break_days'] ?? [];

        // Get position groups for current exercise
        $positionGroups = $this->positionsGroupModel->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->orderBy('priority', 'ASC')
            ->orderBy('group_name', 'ASC')
            ->findAll();

        // Prepare data structure for interview schedule
        $scheduleData = [];
        $currentDate = $startDate;
        $currentDateTime = strtotime($currentDate . ' ' . $startTime);
        $endDateTime = strtotime($currentDate . ' ' . $endTime);
        $totalApplicants = 0;

        // Process each position group
        foreach ($positionGroups as $group) {
            // Get positions marked for interview in this group
            $positions = $this->positionModel->where('position_group_id', $group['id'])
                ->where('exercise_id', $exerciseId)
                ->where('org_id', $orgId)
                ->where('for_interview', 1)
                ->orderBy('position_no', 'ASC')
                ->findAll();

            if (empty($positions)) {
                continue; // Skip if no positions are marked for interview
            }

            $groupData = [
                'id' => $group['id'],
                'name' => $group['group_name'],
                'positions' => []
            ];

            // Process each position
            foreach ($positions as $position) {
                // Get shortlisted applicants for this position
                $applicants = $this->applicantModel->where('position_id', $position['id'])
                    ->where('exercise_id', $exerciseId)
                    ->where('org_id', $orgId)
                    ->where('application_status', 'Shortlisted')
                    ->orderBy('name', 'ASC')
                    ->findAll();

                if (empty($applicants)) {
                    continue; // Skip if no shortlisted applicants
                }

                $totalApplicants += count($applicants);

                $positionData = [
                    'id' => $position['id'],
                    'position_no' => $position['position_no'],
                    'designation' => $position['designation'],
                    'applicants' => []
                ];

                // Process each applicant and schedule their interview
                foreach ($applicants as $applicant) {
                    // Check if current date is a break day
                    while (in_array(date('Y-m-d', $currentDateTime), $breakDays)) {
                        // Move to next day
                        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                        $currentDateTime = strtotime($currentDate . ' ' . $startTime);
                        $endDateTime = strtotime($currentDate . ' ' . $endTime);
                    }

                    // Sort break times by start time to process them in order
                    usort($breakTimes, function($a, $b) {
                        return strtotime($a['start']) - strtotime($b['start']);
                    });

                    // Check if current time falls in any break time
                    $checkingBreaks = true;
                    while ($checkingBreaks) {
                        $checkingBreaks = false;
                        foreach ($breakTimes as $breakTime) {
                            $breakStart = strtotime($currentDate . ' ' . $breakTime['start']);
                            $breakEnd = strtotime($currentDate . ' ' . $breakTime['end']);

                            if ($currentDateTime >= $breakStart && $currentDateTime < $breakEnd) {
                                // Current time is in break time, move to end of break
                                $currentDateTime = $breakEnd;
                                $checkingBreaks = true; // Continue checking in case the new time falls in another break
                                break; // Break the foreach loop but continue the while loop
                            }
                        }
                    }

                    // If we've passed the end time for the day, move to the next day
                    if ($currentDateTime >= $endDateTime) {
                        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                        $currentDateTime = strtotime($currentDate . ' ' . $startTime);
                        $endDateTime = strtotime($currentDate . ' ' . $endTime);

                        // Check if new date is a break day
                        while (in_array($currentDate, $breakDays)) {
                            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                            $currentDateTime = strtotime($currentDate . ' ' . $startTime);
                            $endDateTime = strtotime($currentDate . ' ' . $endTime);
                        }
                    }

                    // Calculate interview end time
                    $interviewEndTime = $currentDateTime + ($minutesPerInterview * 60);

                    // Add applicant to schedule with full data
                    $applicantData = [
                        'id' => $applicant['id'],
                        'name' => $applicant['name'],
                        'contact_details' => $applicant['contact_details'],
                        'date' => date('Y-m-d', $currentDateTime),
                        'start_time' => date('H:i', $currentDateTime),
                        'end_time' => date('H:i', $interviewEndTime),
                        'interview_notices' => $applicant['interview_notices'] ?? null
                    ];

                    // Ensure we have the interview_notices field
                    if (!isset($applicantData['interview_notices'])) {
                        $fullApplicant = $this->applicantModel->find($applicant['id']);
                        if ($fullApplicant) {
                            $applicantData['interview_notices'] = $fullApplicant['interview_notices'] ?? null;
                        }
                    }

                    $positionData['applicants'][] = $applicantData;

                    // Move current time to next interview slot
                    $currentDateTime = $interviewEndTime + ($transitionTime * 60);

                    // Check if new time falls in any break time
                    $checkingBreaks = true;
                    while ($checkingBreaks) {
                        $checkingBreaks = false;
                        foreach ($breakTimes as $breakTime) {
                            $breakStart = strtotime($currentDate . ' ' . $breakTime['start']);
                            $breakEnd = strtotime($currentDate . ' ' . $breakTime['end']);

                            if ($currentDateTime >= $breakStart && $currentDateTime < $breakEnd) {
                                // Current time is in break time, move to end of break
                                $currentDateTime = $breakEnd;
                                $checkingBreaks = true; // Continue checking in case the new time falls in another break
                                break; // Break the foreach loop but continue the while loop
                            }
                        }
                    }
                }

                if (!empty($positionData['applicants'])) {
                    $groupData['positions'][] = $positionData;
                }
            }

            if (!empty($groupData['positions'])) {
                $scheduleData[] = $groupData;
            }
        }

        // Calculate interview end date
        $lastApplicant = null;
        $lastDate = $startDate;

        foreach ($scheduleData as $group) {
            foreach ($group['positions'] as $position) {
                foreach ($position['applicants'] as $applicant) {
                    if (strtotime($applicant['date']) > strtotime($lastDate)) {
                        $lastDate = $applicant['date'];
                        $lastApplicant = $applicant;
                    }
                }
            }
        }

        // Get organization data for display purposes
        $org = $this->dakoiiOrgModel->find($orgId);
        $data['org'] = $org;
        $data['current_exercise'] = get_current_exercise();
        $data['interviewSettings'] = $interviewSettings;
        $data['scheduleData'] = $scheduleData;
        $data['startDate'] = $startDate;
        $data['endDate'] = $lastDate;
        $data['totalApplicants'] = $totalApplicants;

        // Sort all applicants across all positions by date and time
        $allApplicants = [];
        foreach ($scheduleData as $groupIndex => $group) {
            foreach ($group['positions'] as $positionIndex => $position) {
                foreach ($position['applicants'] as $applicantIndex => $applicant) {
                    $applicant['group_name'] = $group['name'];
                    $applicant['position_no'] = $position['position_no'];
                    $applicant['position_designation'] = $position['designation'];
                    $applicant['group_index'] = $groupIndex;
                    $applicant['position_index'] = $positionIndex;
                    $applicant['applicant_index'] = $applicantIndex;

                    // Get the full applicant record to ensure we have interview_notices
                    $fullApplicant = $this->applicantModel->find($applicant['id']);
                    if ($fullApplicant) {
                        // Always include interview_notices field, even if it's empty
                        $applicant['interview_notices'] = $fullApplicant['interview_notices'] ?? null;
                    }

                    $allApplicants[] = $applicant;
                }
            }
        }

        // Sort by date and time
        usort($allApplicants, function($a, $b) {
            $dateComparison = strtotime($a['date']) - strtotime($b['date']);
            if ($dateComparison !== 0) {
                return $dateComparison;
            }
            return strtotime($a['start_time']) - strtotime($b['start_time']);
        });

        $data['sortedApplicants'] = $allApplicants;
        $data['scheduleData'] = $scheduleData;

        return view('reports/interview_schedule', $data);
    }


}
