CREATE TABLE exercises (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    org_id INT UNSIGNED NOT NULL,             -- Reference to organizations table
    exercise_title VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('draft','active','completed','archived') NOT NULL DEFAULT 'draft',
    status_by INT UNSIGNED,                   -- User ID who last updated the status
    status_at DATETIME,
    created_by INT UNSIGNED NOT NULL,         -- User ID who created the exercise
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

ALTER TABLE applicants
  ADD COLUMN exercise_id INT UNSIGNED AFTER org_id;

ALTER TABLE interviewers
  ADD COLUMN exercise_id INT UNSIGNED AFTER id;

ALTER TABLE interview_questions
  ADD COLUMN exercise_id INT UNSIGNED AFTER id;

ALTER TABLE positions
  ADD COLUMN exercise_id INT UNSIGNED AFTER id;

ALTER TABLE positions_groups
  ADD COLUMN exercise_id INT UNSIGNED AFTER id;

ALTER TABLE selection
  ADD COLUMN exercise_id INT UNSIGNED AFTER id;
