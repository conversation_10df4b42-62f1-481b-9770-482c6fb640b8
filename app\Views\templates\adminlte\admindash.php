<?php helper(['info', 'exercise']); ?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?= $title ?></title>

  <!-- Favicon -->
  <link rel="shortcut icon" type="image/x-icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico">

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

  <!-- Bootstrap 4 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- AdminLTE Theme Style -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/css/adminlte.min.css">

  <!-- Page specific styles -->
  <?= $this->renderSection('styles') ?>

  <!-- Javascripts -->
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

  <!-- Bootstrap 4 Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- AdminLTE App -->
  <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/js/adminlte.min.js"></script>

  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">

  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet" href="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">

  <!-- iCheck -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css">

  <!-- JQVMap -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jqvmap.min.css">

  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap4.min.css">

  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.1/css/OverlayScrollbars.min.css">

  <!-- Daterangepicker -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.css">

  <!-- Summernote -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css">

  <!-- Toastr -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

  <!-- Select2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css">

  <!-- In the head section -->
  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

  <!-- jQuery UI 1.11.4 -->
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js" integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
  <script>
    $.widget.bridge('uibutton', $.ui.button);
    
    // Comprehensive fix for tooltip issues
    $(document).ready(function() {
      // Fix for tooltip conflict between jQuery UI and Bootstrap
      if ($.fn.tooltip) {
        // Save original Bootstrap tooltip
        var bootstrapTooltip = $.fn.tooltip;
        
        // Override tooltip to handle both Bootstrap and jQuery UI tooltips
        $.fn.tooltip = function(options) {
          // If this is a jQuery UI tooltip (no Constructor property)
          if (!$.fn.tooltip.Constructor && $(this).data('ui-tooltip')) {
            if (options === 'hide') {
              try {
                // Try to properly close jQuery UI tooltips
                $(this).tooltip('close');
              } catch (e) {
                // If that fails, remove tooltips from DOM
                $('.ui-tooltip').hide();
              }
              return this;
            } else if (options === 'destroy') {
              try {
                $(this).tooltip('destroy');
              } catch (e) {
                $('.ui-tooltip').remove();
              }
              return this;
            }
            
            // If we need to handle other specific commands, add them here
            
            // Default: pass through to jQuery UI tooltip
            return $(this).data('ui-tooltip');
          }
          
          // Otherwise use Bootstrap tooltip
          return bootstrapTooltip.apply(this, arguments);
        };
      }
    });
  </script>

  <!-- ChartJS -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>

  <!-- Sparkline -->
  <script src="https://cdn.jsdelivr.net/npm/jquery-sparkline@2.4.0/jquery.sparkline.min.js"></script>

  <!-- JQVMap -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/jquery.vmap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqvmap/1.5.1/maps/jquery.vmap.usa.js"></script>

  <!-- jQuery Knob -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-Knob/1.2.13/jquery.knob.min.js"></script>

  <!-- Moment.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

  <!-- Daterangepicker -->
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker@3.1.0/daterangepicker.js"></script>

  <!-- Tempusdominus Bootstrap 4 -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>

  <!-- Summernote -->
  <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>

  <!-- overlayScrollbars -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.1/js/jquery.overlayScrollbars.min.js"></script>

  <!-- Toastr -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

  <!-- bs-custom-file-input -->
  <script src="<?= base_url() ?>/public/assets/themes/adminlte320/plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>

  <!-- DataTables & Plugins -->
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap4.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap4.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.70/pdfmake.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.70/vfs_fonts.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.colVis.min.js"></script>

  <!-- Select2 -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.full.min.js"></script>

  <!-- jQuery Mapael -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-mousewheel/3.1.13/jquery.mousewheel.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/raphael/2.3.0/raphael.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-mapael/2.2.0/js/jquery.mapael.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-mapael/2.2.0/js/maps/usa_states.min.js"></script>

  <!-- Before closing body tag, after jQuery -->
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <!-- Add SweetAlert2 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

  <!-- custom codes -->


  <script>
    $(function() {
      bsCustomFileInput.init();
    });
  </script>


  <?php if (session()->has('error')) : ?>
    <span class="toastrDefaultError"></span>
  <?php endif; ?>
  <?php if (session()->has('success')) : ?>
    <span class="toastrDefaultSuccess"></span>
  <?php endif; ?>


  <script>
    $('.toastrDefaultSuccess').show(function() {
      toastr.success('<?= session('success') ?>')
      // toastr.success('Cook Liks')
    });
    $('.toastrDefaultError').show(function() {
      toastr.error('<?= session('error') ?>')
    });
  </script>

  <script>
    $(function() {
      //Initialize Select2 Elements
      $('.select2').select2()

      //Initialize Select2 Elements with Bootstrap 4 theme
      $('.select2bs4').select2({
        theme: 'bootstrap4'
      })
    })
  </script>


</head>

<body class="hold-transition sidebar-mini layout-fixed">
  <div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
      <img class="animation__shake" src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DIPIA" height="60" width="60">
    </div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
      </ul>

      <?= session('org_name') ?>

      <!-- Exercise Context Display -->
      <?php helper('exercise'); ?>
      <?php if (has_exercise_context()): ?>
        <?php $currentExercise = get_current_exercise(); ?>
        <div class="ml-3">
          <span class="badge badge-<?= get_exercise_status_color($currentExercise['status']) ?> badge-lg">
            <i class="fas fa-tasks mr-1"></i>
            <?= esc($currentExercise['exercise_title']) ?>
            <small class="ml-1">(<?= ucfirst($currentExercise['status']) ?>)</small>
          </span>
          <a href="<?= base_url('exercise') ?>" class="btn btn-sm btn-outline-secondary ml-1" title="Change Exercise">
            <i class="fas fa-exchange-alt"></i>
          </a>
        </div>
      <?php endif; ?>

      <!-- Search form -->
      <div class="form-inline ml-3">
        <div class="input-group input-group-sm">
          <input id="applicant-search" class="form-control form-control-navbar" type="search" placeholder="Search Applicants" aria-label="Search">
          <div class="input-group-append">
            <button class="btn btn-navbar" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div id="search-results" class="dropdown-menu position-absolute" style="display: none; top: 100%; left: 0; width: 100%;"></div>
      </div>

      <script>
        // Add this script to your view or in a separate JS file
        $(document).ready(function() {
          let searchTimer;
          const searchInput = $('#applicant-search');
          const searchResults = $('#search-results');

          searchInput.on('input', function() {
            clearTimeout(searchTimer);
            const query = $(this).val().trim();

            if (query.length > 2) {
              searchTimer = setTimeout(function() {
                $.ajax({
                  url: '<?= base_url('applicants/search') ?>',
                  method: 'GET',
                  data: {
                    query: query
                  },
                  dataType: 'json',
                  success: function(response) {
                    searchResults.empty(); // Empty the search results before adding new data
                    if (response.length > 0) {
                      response.forEach(function(applicant) {
                        searchResults.append(`
                                    <a class="dropdown-item" href="#" data-toggle="modal" data-target="#applicantModal${applicant.id}" data-applicant-id="${applicant.id}" data-applicant-name="${applicant.name}">
                                        ${applicant.name} - ${applicant.position_no} | ${applicant.designation}
                                    </a>
                                `);

                        // Add this modal HTML outside of the searchResults.append() call
                        if (!$('#applicantModal').length) {
                          $('body').append(`
                                        <div class="modal fade" id="applicantModal${applicant.id}" tabindex="-1" role="dialog" aria-labelledby="applicantModalLabel" aria-hidden="true">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="applicantModalLabel">Applicant Details</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <h6>Applicant Information</h6>
                                                                <p><strong>Name:</strong> <span id="applicantName"></span></p>
                                                                <p><strong>Sex:</strong> <span id="applicantSex"></span></p>
                                                                <p><strong>Age:</strong> <span id="applicantAge"></span></p>
                                                                <p><strong>Place of Origin:</strong> <span id="applicantOrigin"></span></p>
                                                                <p><strong>Current Employer:</strong> <span id="applicantEmployer"></span></p>
                                                                <p><strong>Current Position:</strong> <span id="applicantCurrentPosition"></span></p>
                                                                <p><strong>Address:</strong> <span id="applicantAddress"></span></p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <h6>Position Information</h6>
                                                                <p><strong>Position:</strong> (<span id="applicantPosition"></span>) <span id="positionDesignation"></span></p>
                                                                <p><strong>Classification:</strong> <span id="positionClassification"></span></p>
                                                                <p><strong>Group:</strong> <span id="positionGroup"></span></p>
                                                                <p><strong>Qualifications:</strong> <span id="positionQualifications"></span></p>
                                                                <p><strong>Knowledge Required:</strong> <span id="positionKnowledge"></span></p>
                                                                <p><strong>Skills & Competencies:</strong> <span id="positionSkills"></span></p>
                                                                <p><strong>Job Experiences:</strong> <span id="positionExperiences"></span></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `);
                        }

                        // Add this event listener after the modal HTML is added
                        $(`#applicantModal${applicant.id}`).on('show.bs.modal', function (event) {
                          const modal = $(this);
                          modal.find('#applicantName').text(applicant.name);
                          modal.find('#applicantSex').text(applicant.sex);
                          modal.find('#applicantAge').text(applicant.age);
                          modal.find('#applicantOrigin').text(applicant.place_origin);
                          modal.find('#applicantEmployer').text(applicant.current_employer);
                          modal.find('#applicantCurrentPosition').text(applicant.current_position);
                          modal.find('#applicantAddress').text(applicant.address_location);
                          modal.find('#applicantPosition').text(applicant.position_no);
                          modal.find('#positionDesignation').text(applicant.designation);
                          modal.find('#positionClassification').text(applicant.group_name);
                          modal.find('#positionGroup').text(applicant.group_name);
                          modal.find('#positionQualifications').text(applicant.qualification_text);
                          modal.find('#positionKnowledge').text(applicant.knowledge);
                          modal.find('#positionSkills').text(applicant.skills_competencies);
                          modal.find('#positionExperiences').text(applicant.job_experiences);
                        });
                      });
                      searchResults.show();
                    } else {
                      searchResults.hide();
                      searchResults.empty();
                    }
                  },
                  error: function(xhr, status, error) {
                    console.error('Error in search:', error);
                    searchResults.empty();
                    searchResults.hide();
                  }
                });
              }, 300);
            } else {
              searchResults.empty();
              searchResults.hide();
            }
          });

          // Hide search results when clicking outside
          $(document).on('click', function(event) {
            if (!$(event.target).closest('#applicant-search, #search-results').length) {
              searchResults.empty();
              searchResults.hide();
            }
          });

          // Empty search results, clear input, and clear modal content when modal closes or hides
          $(document).on('hidden.bs.modal', '.modal', function () {
            searchResults.empty();
            searchInput.val('');
            $(this).find('.modal-body span').text('');
          });
        });
      </script>

      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">
        <li class="nav-item dropdown">
          <a class="nav-link" data-toggle="dropdown" href="#" aria-expanded="false">
            <i class="fas fa-font"></i> <span class="d-none d-sm-inline-block ml-1">Font Size</span>
          </a>
          <div class="dropdown-menu dropdown-menu-right p-2" style="min-width: 200px">
            <div class="font-size-controls">
              <button class="btn btn-sm btn-default" id="decrease-font"><i class="fas fa-minus"></i></button>
              <button class="btn btn-sm btn-default mx-1" id="reset-font"><i class="fas fa-undo"></i></button>
              <button class="btn btn-sm btn-default" id="increase-font"><i class="fas fa-plus"></i></button>
              <span class="ml-2" id="font-size-value">100%</span>
            </div>
          </div>
        </li>
        <li class="nav-item">
          <a class="nav-link" data-widget="fullscreen" href="#" role="button">
            <i class="fas fa-expand-arrows-alt"></i>
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" data-widget="control-sidebar" data-controlsidebar-slide="true" href="#" role="button">
            <i class="fas fa-th-large"></i>
          </a>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <!-- Brand Logo -->
      <a href="#" class="brand-link">
        <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DIPIA" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">SelMasta</span>
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
          <div class="image">
            <img src="<?= imgcheck('') ?>" class="img-circle elevation-2" alt="User Image">
          </div>
          <div class="info">
            <a href="#" class="d-block"><?= session('user_fname') ?> <?= session('user_lname') ?></a>
          </div>
        </div>

        <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            <!-- Add icons to the links using the .nav-icon class with font-awesome or any other icon font library -->

            <li class="nav-item">
              <?php $active = ($menu == "dashboard") ? "active" : ""; ?>
              <a href="<?= base_url('dashboard') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-tachometer-alt"></i>
                <p>Dashboard</p>
              </a>
            </li>

            <li class="nav-item">
              <?php $active = ($menu == "exercises") ? "active" : ""; ?>
              <a href="<?= base_url('exercise') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-tasks"></i>
                <p>Exercises</p>
              </a>
            </li>

            <!-- Exercise-dependent menu items (only show when exercise is selected) -->
            <?php if (has_exercise_context()): ?>
            <li class="nav-item">
              <?php $active = ($menu == "positions") ? "active" : ""; ?>
              <a href="<?= base_url('positions') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-briefcase"></i>
                <p>Positions</p>
              </a>
            </li>

            <li class="nav-item">
              <?php $active = ($menu == "applicants") ? "active" : ""; ?>
              <a href="<?= base_url('applicants') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-user"></i>
                <p>Applicants</p>
              </a>
            </li>

            <li class="nav-item">
              <?php $active = ($menu == "ratings") ? "active" : ""; ?>
              <a href="<?= base_url('ratings') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-star"></i>
                <p>Ratings</p>
              </a>
            </li>

            <li class="nav-item">
              <?php $active = ($menu == "interviews") ? "active" : ""; ?>
              <a href="<?= base_url('interviews') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-comments"></i>
                <p>Interviews</p>
              </a>
            </li>

            <li class="nav-item">
              <?php $active = ($menu == "reports") ? "active" : ""; ?>
              <a href="<?= base_url('reports') ?>" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-chart-bar"></i>
                <p>Reports</p>
              </a>
            </li>
            <?php endif; ?>

            <li class="nav-item <?= ($menu == "settings") ? "menu-open" : "" ?>">
              <?php $active = ($menu == "settings") ? "active" : ""; ?>
              <a href="#" class="nav-link <?= $active ?>">
                <i class="nav-icon fas fa-cog"></i>
                <p>
                  Settings
                  <i class="right fas fa-angle-left"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="<?= base_url('settings') ?>" class="nav-link <?= ($menu == "settings") ? "active" : "" ?>">
                    <i class="far fa-circle nav-icon"></i>
                    <p>Organization Settings</p>
                  </a>
                </li>
                <?php helper('exercise'); ?>
                <?php if (has_exercise_context()): ?>
                <li class="nav-item">
                  <a href="<?= base_url('settings/interviews') ?>" class="nav-link">
                    <i class="far fa-calendar-alt nav-icon"></i>
                    <p>Exercise Settings</p>
                  </a>
                </li>
                <?php endif; ?>
              </ul>
            </li>

            <li class="nav-item">
              <a href="<?= base_url('logout') ?>" class="nav-link text-danger">
                <i class="nav-icon fas fa-sign-out-alt"></i>
                <p>Logout</p>
              </a>
            </li>
          </ul>
        </nav>
        <!-- /.sidebar-menu -->
      </div>
      <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <?= $this->renderSection('content') ?>
    <!-- /.content-wrapper -->
    <footer class="main-footer">
      <strong>SelectionMasta Copyright &copy; 2024</strong> Developed by <b>Dakoii Systems</b> in collaboration with <b>Echad Consultancy</b>
      All rights reserved.
      <div class="float-right d-none d-sm-inline-block">
        <b>Version</b> 2.0
      </div>
    </footer>

    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->

  <!-- Add this script just before the closing </body> tag -->
  <script>
    $(document).ready(function() {
      $('#applicant-search').on('input', function() {
        var searchTerm = $(this).val();
        if (searchTerm.length >= 2) {
          $.ajax({
            url: '<?= base_url('applicants/search') ?>',
            method: 'POST',
            data: {
              search: searchTerm
            },
            dataType: 'json',
            success: function(response) {
              var resultsHtml = '';
              if (response.length > 0) {
                response.forEach(function(applicant) {
                  resultsHtml += '<a class="dropdown-item" href="#">' + applicant.name + '</a>';
                });
              } else {
                resultsHtml = '<span class="dropdown-item">No results found</span>';
              }
              $('#search-results').html(resultsHtml).show();
            },
            error: function(xhr, status, error) {
              console.error('Error:', error);
            }
          });
        } else {
          $('#search-results').hide();
        }
      });
    });
  </script>
  
  <!-- Render scripts from content views -->
  <?= $this->renderSection('scripts') ?>

  <!-- Add this just before the closing </body> tag, after the other scripts -->
  <script>
    $(document).ready(function() {
      // Font size adjustment functionality
      let currentFontSize = 100;
      const body = $('body');
      
      // Store the original font sizes for all elements
      const originalFontSizes = {};
      $('body *').each(function() {
        const el = $(this);
        const fontSize = parseFloat(el.css('font-size'));
        if (fontSize) {
          originalFontSizes[this.tagName + '_' + Math.random()] = fontSize;
        }
      });
      
      // Increase font size
      $('#increase-font').on('click', function(e) {
        e.preventDefault();
        if (currentFontSize < 150) {
          currentFontSize += 10;
          updateFontSize();
        }
      });
      
      // Decrease font size
      $('#decrease-font').on('click', function(e) {
        e.preventDefault();
        if (currentFontSize > 70) {
          currentFontSize -= 10;
          updateFontSize();
        }
      });
      
      // Reset font size
      $('#reset-font').on('click', function(e) {
        e.preventDefault();
        currentFontSize = 100;
        updateFontSize();
      });
      
      // Update font size based on currentFontSize percentage
      function updateFontSize() {
        body.css('font-size', currentFontSize + '%');
        $('#font-size-value').text(currentFontSize + '%');
        
        // Save the preference in localStorage
        localStorage.setItem('preferredFontSize', currentFontSize);
      }
      
      // Load saved font size preference if exists
      const savedFontSize = localStorage.getItem('preferredFontSize');
      if (savedFontSize) {
        currentFontSize = parseInt(savedFontSize);
        updateFontSize();
      }
    });
  </script>
</body>

</html>
