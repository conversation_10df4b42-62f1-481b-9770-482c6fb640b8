<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-tasks mr-2"></i>
                        <?= $title ?>
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Exercises</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tasks mr-2"></i>
                                Exercise Management
                            </h3>
                            <div class="card-tools">
                                <a href="<?= base_url('exercise/create') ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i>
                                    Create New Exercise
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($exercises)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">No Exercises Found</h4>
                                    <p class="text-muted">Create your first exercise to start managing recruitment rounds.</p>
                                    <a href="<?= base_url('exercise/create') ?>" class="btn btn-primary">
                                        <i class="fas fa-plus mr-1"></i>
                                        Create New Exercise
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($exercises as $exercise): ?>
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="card h-100 <?= $exercise['status'] === 'active' ? 'border-success' : ($exercise['status'] === 'completed' ? 'border-info' : ($exercise['status'] === 'archived' ? 'border-secondary' : 'border-warning')) ?>">
                                                <div class="card-header <?= $exercise['status'] === 'active' ? 'bg-success' : ($exercise['status'] === 'completed' ? 'bg-info' : ($exercise['status'] === 'archived' ? 'bg-secondary' : 'bg-warning')) ?> text-white">
                                                    <h5 class="card-title mb-0">
                                                        <i class="fas fa-tasks mr-2"></i>
                                                        <?= esc($exercise['exercise_title']) ?>
                                                    </h5>
                                                    <span class="badge badge-light float-right">
                                                        <?= ucfirst($exercise['status']) ?>
                                                    </span>
                                                </div>
                                                <div class="card-body">
                                                    <p class="card-text">
                                                        <?= esc($exercise['description'] ?: 'No description provided') ?>
                                                    </p>
                                                    
                                                    <!-- Statistics -->
                                                    <div class="row text-center mb-3">
                                                        <div class="col-6">
                                                            <div class="info-box bg-light">
                                                                <span class="info-box-icon bg-primary">
                                                                    <i class="fas fa-users"></i>
                                                                </span>
                                                                <div class="info-box-content">
                                                                    <span class="info-box-text">Applicants</span>
                                                                    <span class="info-box-number"><?= $exercise['stats']['applicants_count'] ?></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="info-box bg-light">
                                                                <span class="info-box-icon bg-success">
                                                                    <i class="fas fa-briefcase"></i>
                                                                </span>
                                                                <div class="info-box-content">
                                                                    <span class="info-box-text">Positions</span>
                                                                    <span class="info-box-number"><?= $exercise['stats']['positions_count'] ?></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row text-center">
                                                        <div class="col-6">
                                                            <div class="info-box bg-light">
                                                                <span class="info-box-icon bg-warning">
                                                                    <i class="fas fa-user-tie"></i>
                                                                </span>
                                                                <div class="info-box-content">
                                                                    <span class="info-box-text">Interviewers</span>
                                                                    <span class="info-box-number"><?= $exercise['stats']['interviewers_count'] ?></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="info-box bg-light">
                                                                <span class="info-box-icon bg-info">
                                                                    <i class="fas fa-question-circle"></i>
                                                                </span>
                                                                <div class="info-box-content">
                                                                    <span class="info-box-text">Questions</span>
                                                                    <span class="info-box-number"><?= $exercise['stats']['questions_count'] ?></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        Created: <?= date('M d, Y', strtotime($exercise['created_at'])) ?>
                                                    </small>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="btn-group w-100" role="group">
                                                        <a href="<?= base_url('exercise/select/' . $exercise['id']) ?>" 
                                                           class="btn btn-primary btn-sm">
                                                            <i class="fas fa-play mr-1"></i>
                                                            Select
                                                        </a>
                                                        <a href="<?= base_url('exercise/show/' . $exercise['id']) ?>" 
                                                           class="btn btn-info btn-sm">
                                                            <i class="fas fa-eye mr-1"></i>
                                                            View
                                                        </a>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" 
                                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                <i class="fas fa-cog"></i>
                                                            </button>
                                                            <div class="dropdown-menu">
                                                                <a class="dropdown-item" href="<?= base_url('exercise/edit/' . $exercise['id']) ?>">
                                                                    <i class="fas fa-edit mr-2"></i>Edit
                                                                </a>
                                                                <?php if ($exercise['status'] === 'draft'): ?>
                                                                    <a class="dropdown-item" href="<?= base_url('exercise/activate/' . $exercise['id']) ?>">
                                                                        <i class="fas fa-play mr-2"></i>Activate
                                                                    </a>
                                                                <?php endif; ?>
                                                                <?php if ($exercise['status'] === 'active'): ?>
                                                                    <a class="dropdown-item" href="<?= base_url('exercise/complete/' . $exercise['id']) ?>">
                                                                        <i class="fas fa-check mr-2"></i>Complete
                                                                    </a>
                                                                <?php endif; ?>
                                                                <?php if ($exercise['status'] === 'completed'): ?>
                                                                    <a class="dropdown-item" href="<?= base_url('exercise/archive/' . $exercise['id']) ?>">
                                                                        <i class="fas fa-archive mr-2"></i>Archive
                                                                    </a>
                                                                <?php endif; ?>
                                                                <div class="dropdown-divider"></div>
                                                                <?php if ($exercise['stats']['applicants_count'] == 0 && $exercise['stats']['positions_count'] == 0): ?>
                                                                    <a class="dropdown-item text-danger" href="<?= base_url('exercise/delete/' . $exercise['id']) ?>" 
                                                                       onclick="return confirm('Are you sure you want to delete this exercise?')">
                                                                        <i class="fas fa-trash mr-2"></i>Delete
                                                                    </a>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?= $this->endSection() ?>
