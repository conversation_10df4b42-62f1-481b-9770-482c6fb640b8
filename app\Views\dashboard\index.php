<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Dashboard
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><i class="fas fa-home"></i></li>
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <!-- Quick Access Menu -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-th-large mr-2"></i>
                                Quick Access Menu
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Exercise Context Alert -->
                            <?php helper('exercise'); ?>
                            <?php if (has_exercise_context()): ?>
                                <?php $currentExercise = get_current_exercise(); ?>
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-tasks mr-2"></i>
                                            <strong>Current Exercise:</strong> <?= esc($currentExercise['exercise_title']) ?>
                                            <span class="badge badge-<?= get_exercise_status_color($currentExercise['status']) ?> ml-2">
                                                <?= ucfirst($currentExercise['status']) ?>
                                            </span>
                                        </div>
                                        <div>
                                            <a href="<?= base_url('settings/interviews') ?>" class="btn btn-sm btn-info mr-2">
                                                <i class="fas fa-cog mr-1"></i> Exercise Settings
                                            </a>
                                            <a href="<?= base_url('exercise') ?>" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-exchange-alt mr-1"></i> Change
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                            <strong>No Exercise Selected:</strong> Select an exercise to access exercise-specific features.
                                        </div>
                                        <a href="<?= base_url('exercise') ?>" class="btn btn-sm btn-warning">
                                            <i class="fas fa-tasks mr-1"></i> Select Exercise
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('positions') ?>" class="btn btn-lg btn-primary btn-block">
                                        <i class="fas fa-briefcase mb-2"></i><br>
                                        Positions
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('applicants') ?>" class="btn btn-lg btn-success btn-block">
                                        <i class="fas fa-users mb-2"></i><br>
                                        Applicants
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('ratings') ?>" class="btn btn-lg btn-warning btn-block">
                                        <i class="fas fa-star mb-2"></i><br>
                                        Ratings
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('reports') ?>" class="btn btn-lg btn-info btn-block">
                                        <i class="fas fa-chart-bar mb-2"></i><br>
                                        Reports
                                    </a>
                                </div>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <a href="<?= base_url('settings') ?>" class="btn btn-lg btn-secondary btn-block">
                                        <i class="fas fa-cog mb-2"></i><br>
                                        Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info boxes -->
            <div class="row">
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-primary elevation-1">
                            <i class="fas fa-briefcase"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Positions</span>
                            <span class="info-box-number">
                                <?= number_format($positions_count) ?>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success elevation-1">
                            <i class="fas fa-users"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Applicants</span>
                            <span class="info-box-number">
                                <?= number_format($applicants_count) ?>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning elevation-1">
                            <i class="fas fa-star"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Rated Applicants</span>
                            <span class="info-box-number">
                                <?= number_format($ratings_count) ?>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: <?= $completion_rate ?>%"></div>
                            </div>
                            <span class="progress-description">
                                <?= $completion_rate ?>% Completion Rate
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info elevation-1">
                            <i class="fas fa-building"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text">Organization</span>
                            <span class="info-box-number text-sm">
                                <?= session('orgname') ?>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Recent Applicants -->
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-clock mr-2"></i>
                                Recent Applicants
                            </h3>
                            <div class="card-tools">
                                <a href="<?= base_url('applicants') ?>" class="btn btn-tool">
                                    <i class="fas fa-users"></i> View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Position</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_applicants as $applicant): ?>
                                        <tr>
                                            <td><?= esc($applicant['name']) ?></td>
                                            <!-- position number -->
                                            <td><?= esc($applicant['position_no']) ?></td>
                                            <td>
                                                <?php if ($applicant['rate_qualification'] != 0): ?>
                                                    <span class="badge badge-success">Rated</span>
                                                <?php else: ?>
                                                    <span class="badge badge-warning">Pending</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Positions -->
                <div class="col-md-6">
                    <div class="card card-primary card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-trophy mr-2"></i>
                                Top Positions by Applicants
                            </h3>
                            <div class="card-tools">
                                <a href="<?= base_url('positions') ?>" class="btn btn-tool">
                                    <i class="fas fa-briefcase"></i> View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Position</th>
                                            <th>Applicants</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_positions as $position): ?>
                                        <tr>
                                            <td><?= esc($position['designation']) ?></td>
                                            <td><?= number_format($position['applicant_count']) ?></td>
                                            <td>
                                                <?php if ($position['is_active']): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Closed</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.btn-lg {
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    white-space: normal;
    text-align: center;
    padding: 10px;
}

.btn-lg i {
    font-size: 2rem;
    margin-bottom: 5px;
}

@media (max-width: 768px) {
    .btn-lg {
        height: 80px;
        font-size: 0.8rem;
    }
    
    .btn-lg i {
        font-size: 1.5rem;
    }
}
</style>
<?= $this->endSection() ?>
