<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-cog mr-2"></i>
                        System Settings
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item active">Settings</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Exercise-Specific Settings Section -->
            <?php helper('exercise'); ?>
            <?php if (has_exercise_context()): ?>
                <?php $current_exercise = get_current_exercise(); ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-dumbbell mr-2"></i>Exercise-Specific Settings</h5>
                    <p class="mb-2">The following settings are specific to your current exercise: <strong><?= esc($current_exercise['exercise_title']) ?></strong></p>
                    <div class="btn-group" role="group">
                        <a href="<?= base_url('settings/interviews') ?>" class="btn btn-info">
                            <i class="fas fa-calendar-alt mr-2"></i> Interview Settings
                        </a>
                        <!-- Add more exercise-specific settings buttons here as needed -->
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle mr-2"></i>Exercise Settings Unavailable</h5>
                    <p class="mb-2">Exercise-specific settings (like Interview Settings) require an active exercise context.</p>
                    <a href="<?= base_url('exercise') ?>" class="btn btn-warning">
                        <i class="fas fa-tasks mr-2"></i> Select an Exercise
                    </a>
                </div>
            <?php endif; ?>

            <!-- Organization-Level Settings Section -->
            <div class="mb-3">
                <h5><i class="fas fa-building mr-2"></i>Organization Settings</h5>
                <p class="text-muted mb-3">These settings apply to your entire organization across all exercises.</p>
                <a href="<?= base_url('settings/pre_selection_report') ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-file-alt mr-2"></i> Pre-Selection Report
                </a>
            </div>
            
            <!-- Exercise Settings Migration Notice -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-info card-outline">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-info-circle mr-2"></i>
                                Exercise-Specific Settings Migration
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info mb-3">
                                <h5><i class="fas fa-arrow-right mr-2"></i>Settings Have Been Moved</h5>
                                <p class="mb-2">The following settings are now exercise-specific and have been moved:</p>
                                <ul class="mb-2">
                                    <li><strong>Advertisement Settings</strong> (Number, Date, Mode)</li>
                                    <li><strong>Sort Out Of</strong> (Maximum score for ranking)</li>
                                    <li><strong>Interview Settings</strong> (Schedule, timing, venue)</li>
                                    <li><strong>Report Content</strong> (Introduction, composition, criteria, culling)</li>
                                </ul>
                                <p class="mb-0">These settings can now be configured independently for each recruitment exercise.</p>
                            </div>

                            <?php if (has_exercise_context()): ?>
                                <div class="text-center">
                                    <a href="<?= base_url('settings/interviews') ?>" class="btn btn-primary btn-lg mr-2">
                                        <i class="fas fa-cog mr-2"></i> Configure Exercise Settings
                                    </a>
                                    <a href="<?= base_url('exercise') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-tasks mr-2"></i> Manage Exercises
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center">
                                    <p class="text-muted mb-3">Select an exercise to configure these settings</p>
                                    <a href="<?= base_url('exercise') ?>" class="btn btn-warning btn-lg">
                                        <i class="fas fa-tasks mr-2"></i> Select an Exercise
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Organization Logo -->
                    <div class="card card-primary card-outline">
                        <div class="card-header d-flex align-items-center">
                            <h3 class="card-title">
                                <i class="fas fa-image mr-2"></i>
                                Organization Logo
                            </h3>
                            <?php if ($org['org_logo']): ?>
                                <img src="<?= imgcheck($org['org_logo']) ?>" alt="Logo" class="ml-auto" style="width: 40px; height: 40px; object-fit: contain;">
                            <?php endif; ?>
                        </div>
                        <?= form_open_multipart('settings_logo') ?>
                        <div class="card-body">
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="logo" name="logo" accept="image/*">
                                <label class="custom-file-label" for="logo">Choose file</label>
                            </div>
                            <small class="form-text text-muted mt-2">
                                Recommended size: 200x200 pixels. Maximum file size: 2MB.
                            </small>
                        </div>
                        <div class="card-footer bg-white">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload mr-1"></i> Upload Logo
                            </button>
                        </div>
                        </form>
                    </div>
                </div>

                
            </div>
        </div>
    </section>
</div>

<style>
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control {
    border-radius: 0.25rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-file-label {
    overflow: hidden;
}

textarea {
    min-height: 120px;
}

.note-editor.note-frame {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.note-editor.note-frame .note-editing-area {
    background-color: #fff;
}
</style>

<script>
$(function() {
    // Initialize all Summernote editors with consistent options
    $('textarea').each(function() {
        $(this).summernote({
            height: 120,
            toolbar: [
                ['style', ['bold', 'italic', 'underline', 'clear']],
                ['para', ['ul', 'ol']],
            ],
            callbacks: {
                onChange: function(contents) {
                    $(this).val(contents);
                }
            }
        });
    });

    // Update custom file input label
    bsCustomFileInput.init();
});
</script>

<?= $this->endSection() ?>